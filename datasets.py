# SWIM-APPAFL集成数据集处理
# 采用SWIM的数据集处理和非独立同分布划分方法
import os
import numpy as np
import torch
import torchvision
import torchvision.transforms as transforms
from torch.utils.data import Dataset, DataLoader
from torchvision.datasets import CIFAR10, CIFAR100, MNIST, FashionMNIST
from PIL import Image
import logging

# 配置日志
logging.basicConfig()
logger = logging.getLogger()
logger.setLevel(logging.INFO)


class CIFAR10_truncated(Dataset):
    """
    CIFAR-10截断数据集类
    支持根据指定的数据索引创建子数据集，用于联邦学习中的数据分割
    """
    def __init__(self, root, dataidxs=None, train=True, transform=None, target_transform=None, download=False):
        """
        初始化CIFAR-10截断数据集
        Args:
            root: 数据根目录
            dataidxs: 数据索引列表（如果为None则使用全部数据）
            train: 是否为训练集
            transform: 数据变换
            target_transform: 标签变换
            download: 是否下载数据
        """
        self.root = root
        self.dataidxs = dataidxs
        self.train = train
        self.transform = transform
        self.target_transform = target_transform
        self.download = download

        # 构建截断后的数据集
        self.data, self.target = self.__build_truncated_dataset__()

    def __build_truncated_dataset__(self):
        """
        构建截断数据集
        Returns:
            tuple: (data, target) 截断后的数据和标签
        """
        # 加载原始CIFAR-10数据集
        cifar_dataobj = CIFAR10(self.root, self.train, self.transform, self.target_transform, self.download)

        # 兼容不同版本的torchvision
        if torchvision.__version__ == '0.2.1':
            if self.train:
                data, target = cifar_dataobj.train_data, np.array(cifar_dataobj.train_labels)
            else:
                data, target = cifar_dataobj.test_data, np.array(cifar_dataobj.test_labels)
        else:
            data = cifar_dataobj.data
            target = np.array(cifar_dataobj.targets)

        # 如果指定了数据索引，则只保留相应的数据
        if self.dataidxs is not None:
            data = data[self.dataidxs]
            target = target[self.dataidxs]

        return data, target

    def __getitem__(self, index):
        """
        获取单个数据样本
        Args:
            index: 数据索引
        Returns:
            tuple: (img, target) 图像和标签
        """
        img, target = self.data[index], self.target[index]
        
        # 转换为PIL图像格式
        from PIL import Image
        img = Image.fromarray(img)

        # 应用数据变换
        if self.transform is not None:
            img = self.transform(img)

        if self.target_transform is not None:
            target = self.target_transform(target)

        return img, target

    def __len__(self):
        return len(self.data)


class MNIST_truncated(Dataset):
    """
    MNIST截断数据集类
    支持根据指定的数据索引创建子数据集，用于联邦学习中的数据分割
    """
    def __init__(self, root, dataidxs=None, train=True, transform=None, target_transform=None, download=False):
        """
        初始化MNIST截断数据集
        Args:
            root: 数据根目录
            dataidxs: 数据索引列表（如果为None则使用全部数据）
            train: 是否为训练集
            transform: 数据变换
            target_transform: 标签变换
            download: 是否下载数据
        """
        self.root = root
        self.dataidxs = dataidxs
        self.train = train
        self.transform = transform
        self.target_transform = target_transform
        self.download = download

        # 构建截断后的数据集
        self.data, self.target = self.__build_truncated_dataset__()

    def __build_truncated_dataset__(self):
        """
        构建截断数据集
        Returns:
            tuple: (data, target) 截断后的数据和标签
        """
        # 加载原始MNIST数据集
        mnist_dataobj = MNIST(self.root, self.train, self.transform, self.target_transform, self.download)

        # 如果指定了数据索引，则只使用这些索引对应的数据
        if self.dataidxs is not None:
            data = mnist_dataobj.data[self.dataidxs]
            target = mnist_dataobj.targets[self.dataidxs]
        else:
            data = mnist_dataobj.data
            target = mnist_dataobj.targets

        return data, target

    def __getitem__(self, index):
        """
        获取指定索引的数据项
        Args:
            index: 数据索引
        Returns:
            tuple: (image, target) 图像和标签
        """
        img, target = self.data[index], int(self.target[index])

        # 将PIL图像转换为PIL Image对象（如果需要）
        if not isinstance(img, Image.Image):
            # MNIST数据是numpy数组，需要转换为PIL Image
            img = Image.fromarray(img.numpy(), mode='L')

        # 应用变换
        if self.transform is not None:
            img = self.transform(img)

        if self.target_transform is not None:
            target = self.target_transform(target)

        return img, target

    def __len__(self):
        """返回数据集大小"""
        return len(self.data)


class FashionMNIST_truncated(Dataset):
    """
    Fashion-MNIST截断数据集类
    支持根据指定的数据索引创建子数据集，用于联邦学习中的数据分割
    """
    def __init__(self, root, dataidxs=None, train=True, transform=None, target_transform=None, download=False):
        """
        初始化Fashion-MNIST截断数据集
        Args:
            root: 数据根目录
            dataidxs: 数据索引列表（如果为None则使用全部数据）
            train: 是否为训练集
            transform: 数据变换
            target_transform: 标签变换
            download: 是否下载数据
        """
        self.root = root
        self.dataidxs = dataidxs
        self.train = train
        self.transform = transform
        self.target_transform = target_transform
        self.download = download

        # 构建截断后的数据集
        self.data, self.target = self.__build_truncated_dataset__()

    def __build_truncated_dataset__(self):
        """
        构建截断数据集
        Returns:
            tuple: (data, target) 截断后的数据和标签
        """
        # 加载原始Fashion-MNIST数据集
        fashion_dataobj = FashionMNIST(self.root, self.train, self.transform, self.target_transform, self.download)

        # 如果指定了数据索引，则只使用这些索引对应的数据
        if self.dataidxs is not None:
            data = fashion_dataobj.data[self.dataidxs]
            target = fashion_dataobj.targets[self.dataidxs]
        else:
            data = fashion_dataobj.data
            target = fashion_dataobj.targets

        return data, target

    def __getitem__(self, index):
        """
        获取指定索引的数据项
        Args:
            index: 数据索引
        Returns:
            tuple: (image, target) 图像和标签
        """
        img, target = self.data[index], int(self.target[index])

        # 将PIL图像转换为PIL Image对象（如果需要）
        if not isinstance(img, Image.Image):
            # Fashion-MNIST数据是numpy数组，需要转换为PIL Image
            img = Image.fromarray(img.numpy(), mode='L')

        # 应用变换
        if self.transform is not None:
            img = self.transform(img)

        if self.target_transform is not None:
            target = self.target_transform(target)

        return img, target

    def __len__(self):
        """返回数据集大小"""
        return len(self.data)


class CIFAR100_truncated(Dataset):
    """
    CIFAR-100截断数据集类
    支持根据指定的数据索引创建子数据集，用于联邦学习中的数据分割
    """
    def __init__(self, root, dataidxs=None, train=True, transform=None, target_transform=None, download=False):
        """
        初始化CIFAR-100截断数据集
        Args:
            root: 数据根目录
            dataidxs: 数据索引列表（如果为None则使用全部数据）
            train: 是否为训练集
            transform: 数据变换
            target_transform: 标签变换
            download: 是否下载数据
        """
        self.root = root
        self.dataidxs = dataidxs
        self.train = train
        self.transform = transform
        self.target_transform = target_transform
        self.download = download

        # 构建截断后的数据集
        self.data, self.target = self.__build_truncated_dataset__()

    def __build_truncated_dataset__(self):
        """
        构建截断数据集
        Returns:
            tuple: (data, target) 截断后的数据和标签
        """
        # 加载原始CIFAR-100数据集
        cifar_dataobj = CIFAR100(self.root, self.train, self.transform, self.target_transform, self.download)

        # 兼容不同版本的torchvision
        if torchvision.__version__ == '0.2.1':
            if self.train:
                data, target = cifar_dataobj.train_data, np.array(cifar_dataobj.train_labels)
            else:
                data, target = cifar_dataobj.test_data, np.array(cifar_dataobj.test_labels)
        else:
            data = cifar_dataobj.data
            target = np.array(cifar_dataobj.targets)

        # 如果指定了数据索引，则只保留相应的数据
        if self.dataidxs is not None:
            data = data[self.dataidxs]
            target = target[self.dataidxs]

        return data, target

    def __getitem__(self, index):
        """
        获取单个数据样本
        Args:
            index: 数据索引
        Returns:
            tuple: (img, target) 图像和标签
        """
        img, target = self.data[index], self.target[index]
        
        # 转换为PIL图像格式
        from PIL import Image
        img = Image.fromarray(img)

        # 应用数据变换
        if self.transform is not None:
            img = self.transform(img)

        if self.target_transform is not None:
            target = self.target_transform(target)

        return img, target

    def __len__(self):
        return len(self.data)


def load_cifar10_data(datadir):
    """
    加载CIFAR-10数据集
    Args:
        datadir: 数据目录路径
    Returns:
        tuple: (X_train, y_train, X_test, y_test) 训练和测试数据
    """
    # 定义基本的数据变换（仅转换为张量）
    transform = transforms.Compose([transforms.ToTensor()])

    # 创建CIFAR-10数据集对象
    cifar10_train_ds = CIFAR10_truncated(datadir, train=True, download=True, transform=transform)
    cifar10_test_ds = CIFAR10_truncated(datadir, train=False, download=True, transform=transform)

    # 提取数据和标签
    X_train, y_train = cifar10_train_ds.data, cifar10_train_ds.target
    X_test, y_test = cifar10_test_ds.data, cifar10_test_ds.target

    return (X_train, y_train, X_test, y_test)


def load_cifar100_data(datadir):
    """
    加载CIFAR-100数据集
    Args:
        datadir: 数据目录路径
    Returns:
        tuple: (X_train, y_train, X_test, y_test) 训练和测试数据
    """
    # 定义基本的数据变换（仅转换为张量）
    transform = transforms.Compose([transforms.ToTensor()])

    # 创建CIFAR-100数据集对象
    cifar100_train_ds = CIFAR100_truncated(datadir, train=True, download=True, transform=transform)
    cifar100_test_ds = CIFAR100_truncated(datadir, train=False, download=True, transform=transform)

    # 提取数据和标签
    X_train, y_train = cifar100_train_ds.data, cifar100_train_ds.target
    X_test, y_test = cifar100_test_ds.data, cifar100_test_ds.target

    return (X_train, y_train, X_test, y_test)


def load_mnist_data(datadir):
    """
    加载MNIST数据集
    Args:
        datadir: 数据目录路径
    Returns:
        tuple: (X_train, y_train, X_test, y_test) 训练和测试数据
    """
    # 定义基本的数据变换（仅转换为张量）
    transform = transforms.Compose([transforms.ToTensor()])

    # 创建MNIST数据集对象
    mnist_train_ds = MNIST_truncated(datadir, train=True, download=True, transform=transform)
    mnist_test_ds = MNIST_truncated(datadir, train=False, download=True, transform=transform)

    # 提取数据和标签
    X_train, y_train = mnist_train_ds.data, mnist_train_ds.target
    X_test, y_test = mnist_test_ds.data, mnist_test_ds.target

    return (X_train, y_train, X_test, y_test)


def load_fashion_mnist_data(datadir):
    """
    加载Fashion-MNIST数据集
    Args:
        datadir: 数据目录路径
    Returns:
        tuple: (X_train, y_train, X_test, y_test) 训练和测试数据
    """
    # 定义基本的数据变换（仅转换为张量）
    transform = transforms.Compose([transforms.ToTensor()])

    # 创建Fashion-MNIST数据集对象
    fashion_train_ds = FashionMNIST_truncated(datadir, train=True, download=True, transform=transform)
    fashion_test_ds = FashionMNIST_truncated(datadir, train=False, download=True, transform=transform)

    # 提取数据和标签
    X_train, y_train = fashion_train_ds.data, fashion_train_ds.target
    X_test, y_test = fashion_test_ds.data, fashion_test_ds.target

    return (X_train, y_train, X_test, y_test)


def record_net_data_stats(y_train, net_dataidx_map, logdir):
    """
    记录每个客户端的数据统计信息
    Args:
        y_train: 训练数据标签
        net_dataidx_map: 网络数据索引映射（客户端ID -> 数据索引列表）
        logdir: 日志目录
    Returns:
        net_cls_counts: 每个客户端的类别数据统计
    """
    net_cls_counts = {}

    # 统计每个客户端的类别分布
    for net_i, dataidx in net_dataidx_map.items():
        # 获取当前客户端数据的唯一类别和对应数量
        unq, unq_cnt = np.unique(y_train[dataidx], return_counts=True)
        # 创建类别到数量的映射
        tmp = {unq[i]: unq_cnt[i] for i in range(len(unq))}
        net_cls_counts[net_i] = tmp

    # 计算每个客户端的总数据量
    data_list = []
    for net_id, data in net_cls_counts.items():
        n_total = 0
        for class_id, n_data in data.items():
            n_total += n_data
        data_list.append(n_total)

    # 打印数据分布的统计信息
    print('平均数据量:', np.mean(data_list))
    print('数据量标准差:', np.std(data_list))
    logger.info('数据统计: %s' % str(net_cls_counts))

    return net_cls_counts


def partition_data(dataset, datadir, logdir, partition, n_parties, beta=0.5):
    """
    将数据分割给不同的客户端
    支持同质（IID）和异质（Non-IID）数据分布
    采用SWIM的数据划分方法

    Args:
        dataset: 数据集名称 ('cifar10' 或 'cifar100')
        datadir: 数据目录
        logdir: 日志目录
        partition: 分割策略 ("homo"/"iid" 或 "noniid"/"noniid-labeldir")
        n_parties: 客户端数量
        beta: 狄利克雷分布参数（控制数据异质性程度）

    Returns:
        tuple: (X_train, y_train, X_test, y_test, net_dataidx_map, traindata_cls_counts)
    """
    # 根据数据集类型加载相应数据
    if dataset == 'cifar10':
        X_train, y_train, X_test, y_test = load_cifar10_data(datadir)
    elif dataset == 'cifar100':
        X_train, y_train, X_test, y_test = load_cifar100_data(datadir)
    elif dataset == 'mnist':
        X_train, y_train, X_test, y_test = load_mnist_data(datadir)
    elif dataset == 'fashion_mnist':
        X_train, y_train, X_test, y_test = load_fashion_mnist_data(datadir)
    else:
        raise ValueError(f"不支持的数据集: {dataset}")

    n_train = y_train.shape[0]  # 训练样本总数
    net_dataidx_map = {}  # 客户端数据索引映射

    # 同质数据分布（IID）：随机均匀分配数据
    if partition == "homo" or partition == "iid":
        print("使用IID数据分布")
        # 随机打乱所有训练数据索引
        idxs = np.random.permutation(n_train)
        # 将索引均匀分配给各个客户端
        batch_idxs = np.array_split(idxs, n_parties)
        net_dataidx_map = {i: batch_idxs[i] for i in range(n_parties)}

    # 异质数据分布（Non-IID）：使用狄利克雷分布控制数据异质性
    elif partition == "noniid-labeldir" or partition == "noniid":
        print(f"使用Non-IID数据分布，beta={beta}")
        min_size = 0  # 当前最小客户端数据量
        min_require_size = 10  # 要求的最小数据量

        # 根据数据集确定类别数
        K = 10 if dataset == 'cifar10' else 100

        N = y_train.shape[0]  # 总样本数

        # 重复分配直到每个客户端都有足够的数据
        idx_batch = [[] for _ in range(n_parties)]
        while min_size < min_require_size:
            idx_batch = [[] for _ in range(n_parties)]

            # 对每个类别进行分配
            for k in range(K):
                # 获取当前类别的所有样本索引
                idx_k = np.where(y_train == k)[0]
                np.random.shuffle(idx_k)

                # 使用狄利克雷分布生成分配比例
                proportions = np.random.dirichlet(np.repeat(beta, n_parties))

                # 确保数据分配的平衡性
                proportions = np.array([p * (len(idx_j) < N / n_parties) for p, idx_j in zip(proportions, idx_batch)])
                proportions = proportions / proportions.sum()

                # 计算分割点
                proportions = (np.cumsum(proportions) * len(idx_k)).astype(int)[:-1]

                # 将当前类别的数据分配给各个客户端
                idx_batch = [idx_j + idx.tolist() for idx_j, idx in zip(idx_batch, np.split(idx_k, proportions))]

                # 更新最小数据量
                min_size = min([len(idx_j) for idx_j in idx_batch])

        # 为每个客户端的数据进行随机打乱
        for j in range(n_parties):
            np.random.shuffle(idx_batch[j])
            net_dataidx_map[j] = idx_batch[j]

    else:
        raise ValueError(f"不支持的数据分割策略: {partition}")

    # 记录数据分布统计信息
    traindata_cls_counts = record_net_data_stats(y_train, net_dataidx_map, logdir)
    return (X_train, y_train, X_test, y_test, net_dataidx_map, traindata_cls_counts)


def get_dataloader(dataset, datadir, train_bs=64, test_bs=100, dataidxs=None):
    """
    创建数据加载器
    采用SWIM的数据预处理方法

    Args:
        dataset: 数据集名称
        datadir: 数据目录
        train_bs: 训练批次大小（默认64）
        test_bs: 测试批次大小（默认100）
        dataidxs: 数据索引（用于客户端数据分割）

    Returns:
        tuple: (train_dl, test_dl, train_ds, test_ds) 训练和测试的数据加载器及数据集
    """
    # 处理CIFAR数据集
    if dataset == 'cifar10':
        dl_obj = CIFAR10_truncated

        # CIFAR-10的数据标准化参数
        normalize = transforms.Normalize(mean=[x / 255.0 for x in [125.3, 123.0, 113.9]],
                                         std=[x / 255.0 for x in [63.0, 62.1, 66.7]])

        # 训练数据变换（包含数据增强）
        transform_train = transforms.Compose([
            transforms.ToTensor(),
            transforms.Lambda(lambda x: torch.nn.functional.pad(x.unsqueeze(0), (4, 4, 4, 4), mode='reflect').squeeze()),
            transforms.ToPILImage(),
            transforms.RandomCrop(32),  # 随机裁剪
            transforms.RandomHorizontalFlip(),  # 随机水平翻转
            transforms.ToTensor(),
            normalize
        ])

        # 测试数据变换（仅标准化）
        transform_test = transforms.Compose([
            transforms.ToTensor(),
            normalize
        ])

    elif dataset == 'mnist':
        dl_obj = MNIST_truncated

        # MNIST的数据标准化参数（单通道灰度图像）
        normalize = transforms.Normalize(mean=[0.1307], std=[0.3081])

        # 训练数据变换（包含数据增强）
        transform_train = transforms.Compose([
            transforms.ToTensor(),
            transforms.RandomRotation(10),  # 随机旋转
            transforms.RandomAffine(0, translate=(0.1, 0.1)),  # 随机平移
            normalize
        ])

        # 测试数据变换（仅标准化）
        transform_test = transforms.Compose([
            transforms.ToTensor(),
            normalize
        ])

    elif dataset == 'fashion_mnist':
        dl_obj = FashionMNIST_truncated

        # Fashion-MNIST的数据标准化参数（单通道灰度图像）
        normalize = transforms.Normalize(mean=[0.2860], std=[0.3530])

        # 训练数据变换（包含数据增强）
        transform_train = transforms.Compose([
            transforms.ToTensor(),
            transforms.RandomHorizontalFlip(),  # 随机水平翻转
            transforms.RandomRotation(10),  # 随机旋转
            transforms.RandomAffine(0, translate=(0.1, 0.1)),  # 随机平移
            normalize
        ])

        # 测试数据变换（仅标准化）
        transform_test = transforms.Compose([
            transforms.ToTensor(),
            normalize
        ])

    elif dataset == 'cifar100':
        dl_obj = CIFAR100_truncated

        # CIFAR-100的数据标准化参数
        normalize = transforms.Normalize(mean=[0.5070751592371323, 0.48654887331495095, 0.4409178433670343],
                                         std=[0.2673342858792401, 0.2564384629170883, 0.27615047132568404])

        # 训练数据变换（包含数据增强）
        transform_train = transforms.Compose([
            transforms.RandomCrop(32, padding=4),  # 随机裁剪（带填充）
            transforms.RandomHorizontalFlip(),     # 随机水平翻转
            transforms.RandomRotation(15),         # 随机旋转
            transforms.ToTensor(),
            normalize
        ])

        # 测试数据变换（仅标准化）
        transform_test = transforms.Compose([
            transforms.ToTensor(),
            normalize
        ])
    else:
        raise ValueError(f"不支持的数据集: {dataset}")

    # 创建训练数据集
    train_ds = dl_obj(datadir, dataidxs=dataidxs, train=True, transform=transform_train, download=True)
    # 创建测试数据集
    test_ds = dl_obj(datadir, dataidxs=None, train=False, transform=transform_test, download=True)

    # 创建数据加载器（支持多进程加载以提升性能）
    import os
    num_workers = min(4, os.cpu_count()) if os.cpu_count() else 0

    train_dl = DataLoader(
        dataset=train_ds,
        batch_size=train_bs,
        shuffle=True,
        drop_last=True,  # 丢弃最后一个不完整的batch，避免BatchNorm错误
        num_workers=num_workers,
        pin_memory=True if torch.cuda.is_available() else False
    )

    test_dl = DataLoader(
        dataset=test_ds,
        batch_size=test_bs,
        shuffle=False,
        drop_last=False,
        num_workers=num_workers,
        pin_memory=True if torch.cuda.is_available() else False
    )

    return train_dl, test_dl, train_ds, test_ds
