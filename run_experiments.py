# SWIM-APPAFL集成实验运行脚本
# 自动运行不同配置的实验进行对比
import subprocess
import os
import time
import argparse


def run_experiment(config):
    """
    运行单个实验
    Args:
        config: 实验配置字典
    Returns:
        bool: 实验是否成功
    """
    # 构建命令行参数
    cmd = ['python', 'main.py']
    for key, value in config.items():
        cmd.extend([f'--{key}', str(value)])
    
    print(f"\n{'='*60}")
    print(f"运行实验: {config.get('experiment_name', '未命名实验')}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        # 运行实验
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1小时超时
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✓ 实验成功完成，耗时: {end_time - start_time:.2f}秒")
            return True
        else:
            print(f"✗ 实验失败")
            print(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ 实验超时（超过1小时）")
        return False
    except Exception as e:
        print(f"✗ 实验异常: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SWIM-APPAFL实验批量运行')
    parser.add_argument('--quick', action='store_true', help='快速测试模式（减少轮数和客户端）')
    parser.add_argument('--dataset', type=str, default='both', choices=['cifar10', 'cifar100', 'both'],
                        help='运行的数据集')
    args = parser.parse_args()
    
    # 基础配置
    base_config = {
        'seed': 42,
        'device': 'cuda',
        'datadir': './data',
        'logdir': './logs',
        'partition': 'noniid',
        'beta': 0.5,
        'lr': 0.01,
        'model': 'cnn'
    }
    
    # 根据模式调整参数
    if args.quick:
        # 快速测试模式
        base_config.update({
            'n_clients': 10,
            'cfraction': 0.5,
            'local_epochs': 2,
            'comm_rounds': 10,
        })
        print("🚀 快速测试模式")
    else:
        # 完整实验模式
        base_config.update({
            'n_clients': 100,
            'cfraction': 0.4,
            'local_epochs': 5,
            'comm_rounds': 100,
        })
        print("🔬 完整实验模式")
    
    # 确定要运行的数据集
    datasets = []
    if args.dataset == 'both':
        datasets = ['cifar10', 'cifar100']
    else:
        datasets = [args.dataset]
    
    # 实验配置列表
    experiments = []
    
    for dataset in datasets:
        # 实验1: APPAFL标准训练
        config1 = base_config.copy()
        config1.update({
            'dataset': dataset,
            'use_swim': 0,
            'experiment_name': f'APPAFL标准训练_{dataset.upper()}'
        })
        experiments.append(config1)
        
        # 实验2: SWIM-APPAFL集成训练
        config2 = base_config.copy()
        config2.update({
            'dataset': dataset,
            'use_swim': 1,
            'out_dim': 256,
            'temperature': 0.5,
            'model_buffer_size': 4,
            'kr': 0.5,
            'experiment_name': f'SWIM-APPAFL集成_{dataset.upper()}'
        })
        experiments.append(config2)
        
        # 实验3: 不同温度参数的SWIM
        if not args.quick:
            for temp in [0.1, 0.3, 0.7]:
                config3 = config2.copy()
                config3.update({
                    'temperature': temp,
                    'experiment_name': f'SWIM-APPAFL_temp{temp}_{dataset.upper()}'
                })
                experiments.append(config3)
            
            # 实验4: 不同滑动窗口参数的SWIM
            for kr in [0.25, 0.75]:
                config4 = config2.copy()
                config4.update({
                    'kr': kr,
                    'experiment_name': f'SWIM-APPAFL_kr{kr}_{dataset.upper()}'
                })
                experiments.append(config4)
    
    # 运行实验
    print(f"\n📋 计划运行 {len(experiments)} 个实验")
    
    successful_experiments = 0
    failed_experiments = 0
    
    for i, config in enumerate(experiments, 1):
        print(f"\n🔄 进度: {i}/{len(experiments)}")
        
        if run_experiment(config):
            successful_experiments += 1
        else:
            failed_experiments += 1
        
        # 实验间隔（避免GPU过热）
        if i < len(experiments):
            print("⏳ 等待5秒后开始下一个实验...")
            time.sleep(5)
    
    # 实验总结
    print(f"\n{'='*60}")
    print(f"📊 实验总结")
    print(f"{'='*60}")
    print(f"总实验数: {len(experiments)}")
    print(f"成功: {successful_experiments}")
    print(f"失败: {failed_experiments}")
    print(f"成功率: {successful_experiments/len(experiments)*100:.1f}%")
    
    if successful_experiments > 0:
        print(f"\n✅ 实验结果保存在 ./logs/ 目录中")
        print(f"📁 结果文件格式: results_<dataset>_<partition>_swim<use_swim>.pt")
    
    if failed_experiments > 0:
        print(f"\n⚠️  有 {failed_experiments} 个实验失败，请检查错误信息")


if __name__ == '__main__':
    main()
