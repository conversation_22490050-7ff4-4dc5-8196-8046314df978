# SWIM-APPAFL集成模型定义
# 完全复制原始SWIM处理CIFAR-100的模型架构，分为投影头版和非投影头版
import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class CNNCifar(nn.Module):
    """
    CIFAR-10/100数据集的标准卷积神经网络模型
    用于APPAFL的标准训练算法
    """
    def __init__(self, n_classes=10):
        super(CNNCifar, self).__init__()
        # 第一个卷积层：输入3通道（RGB），输出32通道，卷积核3x3，填充1
        self.conv1 = nn.Conv2d(3, 32, kernel_size=(3, 3), padding=1)
        # 第二个卷积层：输入32通道，输出64通道，卷积核3x3，填充1
        self.conv2 = nn.Conv2d(32, 64, kernel_size=(3, 3), padding=1)
        # 第三个卷积层：输入64通道，输出128通道，卷积核3x3，填充1
        self.conv3 = nn.Conv2d(64, 128, kernel_size=(3, 3), padding=1)
        # 池化层：2x2最大池化，步长2
        self.pool = nn.MaxPool2d(2, 2)
        # 第一个全连接层：输入128*4*4=2048维，输出1028维
        self.fc1 = nn.Linear(128 * 4 * 4, 1028)
        # 第二个全连接层（输出层）：输入1028维，输出n_classes维
        self.fc2 = nn.Linear(1028, n_classes)
        # Dropout层：丢弃率0.3，用于防止过拟合
        self.dropout = nn.Dropout(0.3)

    def forward(self, x):
        """
        前向传播函数
        Args:
            x: 输入数据，形状为(batch_size, 3, 32, 32)
        Returns:
            x: 输出预测结果，形状为(batch_size, n_classes)
        """
        # 第一个卷积层 + ReLU激活 + 池化
        x = self.pool(F.relu(self.conv1(x)))
        # 第二个卷积层 + ReLU激活 + 池化
        x = self.pool(F.relu(self.conv2(x)))
        # 第三个卷积层 + ReLU激活 + 池化
        x = self.pool(F.relu(self.conv3(x)))
        # Dropout防止过拟合
        x = self.dropout(x)
        # 展平特征图为一维向量
        x = x.view(-1, 128 * 4 * 4)
        # 第一个全连接层 + ReLU激活
        x = F.relu(self.fc1(x))
        # Dropout防止过拟合
        x = self.dropout(x)
        # 第二个全连接层（输出层）
        x = self.fc2(x)
        return x


class CNNMnist(nn.Module):
    """
    MNIST/Fashion-MNIST数据集的标准卷积神经网络模型
    用于APPAFL的标准训练算法
    """
    def __init__(self, n_classes=10):
        super(CNNMnist, self).__init__()
        # 第一个卷积层：输入1通道（灰度），输出32通道，卷积核5x5，填充2
        self.conv1 = nn.Conv2d(1, 32, kernel_size=5, stride=1, padding=2)
        # 第一个池化层：2x2最大池化，步长2
        self.pool1 = nn.MaxPool2d(kernel_size=2, stride=2)
        # 第二个卷积层：输入32通道，输出64通道，卷积核5x5，填充2
        self.conv2 = nn.Conv2d(32, 64, kernel_size=5, stride=1, padding=2)
        # 第二个池化层：2x2最大池化，步长2
        self.pool2 = nn.MaxPool2d(kernel_size=2, stride=2)
        # 第一个全连接层：输入7*7*64=3136维，输出512维
        self.fc1 = nn.Linear(7 * 7 * 64, 512)
        # 第二个全连接层（输出层）：输入512维，输出n_classes维
        self.fc2 = nn.Linear(512, n_classes)
        # Dropout层：丢弃率0.3，用于防止过拟合
        self.dropout = nn.Dropout(0.3)

    def forward(self, x):
        """
        前向传播函数
        Args:
            x: 输入数据，形状为(batch_size, 1, 28, 28)
        Returns:
            x: 输出预测结果，形状为(batch_size, n_classes)
        """
        # 第一个卷积层 + ReLU激活 + 池化
        x = self.pool1(F.relu(self.conv1(x)))
        # 第二个卷积层 + ReLU激活 + 池化
        x = self.pool2(F.relu(self.conv2(x)))
        # 展平特征图为一维向量
        x = x.view(-1, 7 * 7 * 64)
        # 第一个全连接层 + ReLU激活
        x = F.relu(self.fc1(x))
        # Dropout防止过拟合
        x = self.dropout(x)
        # 第二个全连接层（输出层）
        x = self.fc2(x)
        return x


class CNNMnistWithProjection(nn.Module):
    """
    带投影头的MNIST/Fashion-MNIST卷积神经网络模型
    用于SWIM算法的对比学习训练
    """
    def __init__(self, out_dim=256, n_classes=10):
        super(CNNMnistWithProjection, self).__init__()
        # 第一个卷积层：输入1通道（灰度），输出32通道，卷积核5x5，填充2
        self.conv1 = nn.Conv2d(1, 32, kernel_size=5, stride=1, padding=2)
        # 第一个池化层：2x2最大池化，步长2
        self.pool1 = nn.MaxPool2d(kernel_size=2, stride=2)
        # 第二个卷积层：输入32通道，输出64通道，卷积核5x5，填充2
        self.conv2 = nn.Conv2d(32, 64, kernel_size=5, stride=1, padding=2)
        # 第二个池化层：2x2最大池化，步长2
        self.pool2 = nn.MaxPool2d(kernel_size=2, stride=2)
        # 第一个全连接层：输入7*7*64=3136维，输出512维
        self.fc1 = nn.Linear(7 * 7 * 64, 512)
        # Dropout层：丢弃率0.3，用于防止过拟合
        self.dropout = nn.Dropout(0.3)

        # 特征维度
        feature_dim = 512

        # 投影头（用于对比学习）
        self.projection_head = nn.Sequential(
            nn.Linear(feature_dim, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, out_dim)
        )

        # 分类头
        self.classifier = nn.Linear(feature_dim, n_classes)

    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入数据，形状为(batch_size, 1, 28, 28)
        Returns:
            features: 特征表示，形状为(batch_size, 512)
            projection: 投影表示，形状为(batch_size, out_dim)
            classification: 分类结果，形状为(batch_size, n_classes)
        """
        # 特征提取
        x = self.pool1(F.relu(self.conv1(x)))
        x = self.pool2(F.relu(self.conv2(x)))
        x = self.dropout(x)

        # 展平特征
        features = x.view(x.size(0), -1)  # (batch_size, 7*7*64)
        features = F.relu(self.fc1(features))  # (batch_size, 512)

        # 投影头输出（用于对比学习）
        projection = self.projection_head(features)

        # 分类头输出
        classification = self.classifier(features)

        return features, projection, classification


class SimpleCNNMnist(nn.Module):
    """
    简单CNN模型，用于MNIST/Fashion-MNIST数据集
    与原始SWIM的SimpleCNN_header架构一致
    """
    def __init__(self, input_dim=(16*4*4), hidden_dims=[120, 84], output_dim=10, num_ftrs=84):
        super(SimpleCNNMnist, self).__init__()
        # 卷积层
        self.conv1 = nn.Conv2d(1, 6, 5)  # 输入1通道，输出6通道，卷积核5x5
        self.pool = nn.MaxPool2d(2, 2)   # 2x2最大池化
        self.conv2 = nn.Conv2d(6, 16, 5) # 输入6通道，输出16通道，卷积核5x5

        # 全连接层（与原始SWIM一致）
        self.fc1 = nn.Linear(input_dim, hidden_dims[0])  # 第一个全连接层
        self.fc2 = nn.Linear(hidden_dims[0], hidden_dims[1])  # 第二个全连接层
        self.fc3 = nn.Linear(hidden_dims[1], output_dim)  # 输出层

        # 存储特征维度（用于投影头）
        self.num_ftrs = num_ftrs

    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入数据，形状为(batch_size, 1, 28, 28)
        Returns:
            features: 特征表示
            None: 占位符（保持接口一致性）
            classification: 分类结果
        """
        # 卷积层
        x = self.pool(F.relu(self.conv1(x)))  # 第一个卷积+池化
        x = self.pool(F.relu(self.conv2(x)))  # 第二个卷积+池化
        x = x.view(-1, 16 * 4 * 4)  # 展平特征

        # 全连接层
        x = F.relu(self.fc1(x))
        features = F.relu(self.fc2(x))  # 特征表示
        classification = self.fc3(features)  # 分类输出

        return features, None, classification


class SimpleCNNMnistWithProjection(nn.Module):
    """
    带投影头的简单CNN模型，用于MNIST/Fashion-MNIST数据集
    与原始SWIM的SimpleCNN_header架构一致，但添加了投影头
    """
    def __init__(self, input_dim=(16*4*4), hidden_dims=[120, 84], output_dim=10, num_ftrs=84, out_dim=256):
        super(SimpleCNNMnistWithProjection, self).__init__()
        # 卷积层
        self.conv1 = nn.Conv2d(1, 6, 5)  # 输入1通道，输出6通道，卷积核5x5
        self.pool = nn.MaxPool2d(2, 2)   # 2x2最大池化
        self.conv2 = nn.Conv2d(6, 16, 5) # 输入6通道，输出16通道，卷积核5x5

        # 全连接层（与原始SWIM一致）
        self.fc1 = nn.Linear(input_dim, hidden_dims[0])  # 第一个全连接层
        self.fc2 = nn.Linear(hidden_dims[0], hidden_dims[1])  # 第二个全连接层

        # 存储特征维度
        self.num_ftrs = num_ftrs

        # 投影头（用于对比学习）
        self.projection_head = nn.Sequential(
            nn.Linear(num_ftrs, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(128, out_dim)
        )

        # 分类头
        self.classifier = nn.Linear(num_ftrs, output_dim)

    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入数据，形状为(batch_size, 1, 28, 28)
        Returns:
            features: 特征表示
            projection: 投影表示
            classification: 分类结果
        """
        # 卷积层
        x = self.pool(F.relu(self.conv1(x)))  # 第一个卷积+池化
        x = self.pool(F.relu(self.conv2(x)))  # 第二个卷积+池化
        x = x.view(-1, 16 * 4 * 4)  # 展平特征

        # 全连接层
        x = F.relu(self.fc1(x))
        features = F.relu(self.fc2(x))  # 特征表示

        # 投影头输出（用于对比学习）
        projection = self.projection_head(features)

        # 分类头输出
        classification = self.classifier(features)

        return features, projection, classification


class CNNCifarWithProjection(nn.Module):
    """
    带投影头的CIFAR CNN模型，用于SWIM算法
    支持对比学习的三元组输出：(特征表示, 投影表示, 分类结果)
    """
    def __init__(self, out_dim=256, n_classes=10):
        super(CNNCifarWithProjection, self).__init__()
        
        # 特征提取器（与CNNCifar相同的结构）
        self.conv1 = nn.Conv2d(3, 32, kernel_size=(3, 3), padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=(3, 3), padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=(3, 3), padding=1)
        self.pool = nn.MaxPool2d(2, 2)
        self.dropout = nn.Dropout(0.3)
        
        # 特征维度计算：128 * 4 * 4 = 2048
        feature_dim = 128 * 4 * 4
        
        # 改进的投影头（用于对比学习）
        self.projection_head = nn.Sequential(
            nn.Linear(feature_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Linear(256, out_dim)
        )
        
        # 分类头
        self.classifier = nn.Linear(feature_dim, n_classes)
        
    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入数据，形状为(batch_size, 3, 32, 32)
        Returns:
            features: 特征表示，形状为(batch_size, 2048)
            projection: 投影表示，形状为(batch_size, out_dim)
            classification: 分类结果，形状为(batch_size, n_classes)
        """
        # 特征提取
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = self.pool(F.relu(self.conv3(x)))
        x = self.dropout(x)
        
        # 展平特征
        features = x.view(x.size(0), -1)  # (batch_size, 2048)
        
        # 投影头输出（用于对比学习）
        projection = self.projection_head(features)
        
        # 分类头输出
        classification = self.classifier(features)
        
        return features, projection, classification


class SEBlock(nn.Module):
    """
    Squeeze-and-Excitation块
    用于增强特征表达能力，提升模型性能
    """
    def __init__(self, channels, reduction=16):
        super(SEBlock, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        # Squeeze：全局平均池化
        y = self.avg_pool(x).view(b, c)
        # Excitation：全连接层生成权重
        y = self.fc(y).view(b, c, 1, 1)
        # 重新加权特征图
        return x * y.expand_as(x)


class ResidualBlock(nn.Module):
    """
    优化的ResNet残差块（BasicBlock）
    采用改进的结构，增强训练稳定性和特征表达能力
    """
    expansion = 1

    def __init__(self, inchannel, outchannel, stride=1):
        super(ResidualBlock, self).__init__()

        # 第一个卷积块
        self.conv1 = nn.Conv2d(inchannel, outchannel, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(outchannel)

        # 第二个卷积块
        self.conv2 = nn.Conv2d(outchannel, outchannel, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(outchannel)

        # 添加Dropout用于正则化
        self.dropout = nn.Dropout2d(0.1)

        # 跳跃连接路径
        self.shortcut = nn.Sequential()
        if stride != 1 or inchannel != outchannel:
            self.shortcut = nn.Sequential(
                nn.Conv2d(inchannel, outchannel, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(outchannel)
            )

    def forward(self, x):
        # 第一个卷积块
        out = F.relu(self.bn1(self.conv1(x)))

        # 添加dropout进行正则化
        out = self.dropout(out)

        # 第二个卷积块
        out = self.bn2(self.conv2(out))

        # 残差连接
        out += self.shortcut(x)
        out = F.relu(out)
        return out


class BottleneckBlock(nn.Module):
    """
    优化的ResNet瓶颈块（用于ResNet50/101/152）
    采用改进的结构，增强特征提取能力和训练稳定性
    """
    expansion = 4

    def __init__(self, inchannel, outchannel, stride=1):
        super(BottleneckBlock, self).__init__()

        # 1x1卷积（降维）
        self.conv1 = nn.Conv2d(inchannel, outchannel, kernel_size=1, bias=False)
        self.bn1 = nn.BatchNorm2d(outchannel)

        # 3x3卷积（主要特征提取）
        self.conv2 = nn.Conv2d(outchannel, outchannel, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(outchannel)

        # 1x1卷积（升维）
        self.conv3 = nn.Conv2d(outchannel, outchannel * self.expansion, kernel_size=1, bias=False)
        self.bn3 = nn.BatchNorm2d(outchannel * self.expansion)

        # 添加Dropout用于正则化
        self.dropout = nn.Dropout2d(0.1)

        # 添加SE模块（Squeeze-and-Excitation）增强特征表达
        self.se = SEBlock(outchannel * self.expansion, reduction=16)

        # 跳跃连接路径
        self.shortcut = nn.Sequential()
        if stride != 1 or inchannel != outchannel * self.expansion:
            self.shortcut = nn.Sequential(
                nn.Conv2d(inchannel, outchannel * self.expansion, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(outchannel * self.expansion)
            )

    def forward(self, x):
        # 1x1卷积（降维）
        out = F.relu(self.bn1(self.conv1(x)))

        # 3x3卷积（特征提取）
        out = F.relu(self.bn2(self.conv2(out)))
        out = self.dropout(out)  # 添加dropout

        # 1x1卷积（升维）
        out = self.bn3(self.conv3(out))

        # SE注意力机制
        out = self.se(out)

        # 残差连接
        out += self.shortcut(x)
        out = F.relu(out)
        return out


class ResNet(nn.Module):
    """
    优化的ResNet网络架构
    专为CIFAR数据集设计，增强特征提取能力
    """
    def __init__(self, block, layers, num_classes=10):
        super(ResNet, self).__init__()
        self.inchannel = 64

        # 改进的初始卷积层，适配CIFAR-10/100的32x32图像
        self.conv1 = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
        )

        # 四个残差层
        self.layer1 = self.make_layer(block, 64, layers[0], stride=1)
        self.layer2 = self.make_layer(block, 128, layers[1], stride=2)
        self.layer3 = self.make_layer(block, 256, layers[2], stride=2)
        self.layer4 = self.make_layer(block, 512, layers[3], stride=2)

        # 自适应平均池化，更好地处理不同尺寸的特征图
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))

        # 添加dropout用于正则化
        self.dropout = nn.Dropout(0.5)

        # 分类层
        self.fc = nn.Linear(512 * block.expansion, num_classes)

        # 权重初始化
        self._initialize_weights()

    def _initialize_weights(self):
        """改进的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                if m.bias is not None:  # 检查bias是否存在
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                if m.bias is not None:  # 检查bias是否存在
                    nn.init.constant_(m.bias, 0)

    def make_layer(self, block, channels, num_blocks, stride):
        strides = [stride] + [1] * (num_blocks - 1)
        layers = []
        for stride in strides:
            layers.append(block(self.inchannel, channels, stride))
            self.inchannel = channels * block.expansion
        return nn.Sequential(*layers)

    def forward(self, x):
        # 初始卷积
        out = self.conv1(x)

        # 四个残差层
        out = self.layer1(out)
        out = self.layer2(out)
        out = self.layer3(out)
        out = self.layer4(out)

        # 全局平均池化
        out = self.avgpool(out)
        out = out.view(out.size(0), -1)

        # Dropout正则化
        out = self.dropout(out)

        # 分类层
        out = self.fc(out)
        return out


def ResNet18(num_classes=10):
    """构建ResNet-18模型"""
    return ResNet(ResidualBlock, [2, 2, 2, 2], num_classes)


def ResNet50(num_classes=10):
    """构建ResNet-50模型"""
    return ResNet(BottleneckBlock, [3, 4, 6, 3], num_classes)


class ResNetCifarWithProjection(nn.Module):
    """
    带投影头的ResNet模型，用于SWIM算法
    支持CIFAR-10/100数据集
    """
    def __init__(self, out_dim=256, n_classes=10, resnet_type='resnet18'):
        super(ResNetCifarWithProjection, self).__init__()

        # 根据类型选择ResNet架构
        if resnet_type == 'resnet18':
            self.backbone = ResNet18(num_classes=n_classes)
        elif resnet_type == 'resnet50':
            self.backbone = ResNet50(num_classes=n_classes)
        else:
            raise ValueError(f"不支持的ResNet类型: {resnet_type}")

        # 获取ResNet的特征维度
        feature_dim = self.backbone.fc.in_features

        # 移除原始的分类层
        self.backbone.fc = nn.Identity()

        # 改进的投影头（用于对比学习）
        self.projection_head = nn.Sequential(
            nn.Linear(feature_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Linear(256, out_dim)
        )

        # 分类头
        self.classifier = nn.Linear(feature_dim, n_classes)

    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入数据
        Returns:
            features: 特征表示
            projection: 投影表示
            classification: 分类结果
        """
        # 特征提取
        features = self.backbone(x)

        # 投影头输出（用于对比学习）
        projection = self.projection_head(features)

        # 分类头输出
        classification = self.classifier(features)

        return features, projection, classification


class ResNet50CifarWithProjection(nn.Module):
    """
    带投影头的ResNet50模型，用于SWIM算法
    支持CIFAR-10/100数据集
    """
    def __init__(self, out_dim=256, n_classes=10):
        super(ResNet50CifarWithProjection, self).__init__()

        # 使用ResNet50作为特征提取器
        self.backbone = ResNet50(n_classes)

        # 获取ResNet的特征维度
        feature_dim = self.backbone.fc.in_features

        # 移除原始的分类层
        self.backbone.fc = nn.Identity()

        # 改进的投影头（用于对比学习）
        self.projection_head = nn.Sequential(
            nn.Linear(feature_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Linear(256, out_dim)
        )

        # 分类头
        self.classifier = nn.Linear(feature_dim, n_classes)

    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入数据
        Returns:
            features: 特征表示
            projection: 投影表示
            classification: 分类结果
        """
        # 特征提取
        features = self.backbone(x)

        # 投影头输出（用于对比学习）
        projection = self.projection_head(features)

        # 分类头输出
        classification = self.classifier(features)

        return features, projection, classification


class CNNCifarSWIMBackbone(nn.Module):
    """
    SWIM模型的主干网络（无投影头版本）
    与CNNCifarWithProjection使用相同的特征提取器和分类器，但不包含投影头
    用于APPAFL标准训练，确保与SWIM算法的公平对比
    """
    def __init__(self, n_classes=10):
        super(CNNCifarSWIMBackbone, self).__init__()

        # 特征提取器（与CNNCifarWithProjection完全相同）
        self.conv1 = nn.Conv2d(3, 32, kernel_size=(3, 3), padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=(3, 3), padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=(3, 3), padding=1)
        self.pool = nn.MaxPool2d(2, 2)
        self.dropout = nn.Dropout(0.3)

        # 特征维度计算：128 * 4 * 4 = 2048
        feature_dim = 128 * 4 * 4

        # 分类头（与CNNCifarWithProjection完全相同）
        self.classifier = nn.Linear(feature_dim, n_classes)

    def forward(self, x):
        """
        前向传播（标准分类输出）
        Args:
            x: 输入数据，形状为(batch_size, 3, 32, 32)
        Returns:
            classification: 分类结果，形状为(batch_size, n_classes)
        """
        # 特征提取（与CNNCifarWithProjection完全相同的过程）
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = self.pool(F.relu(self.conv3(x)))
        x = self.dropout(x)

        # 展平特征
        features = x.view(x.size(0), -1)  # (batch_size, 2048)

        # 分类头输出
        classification = self.classifier(features)

        return classification


class ResNetCifarSWIMBackbone(nn.Module):
    """
    SWIM ResNet模型的主干网络（无投影头版本）
    与ResNetCifarWithProjection使用相同的特征提取器和分类器，但不包含投影头
    用于APPAFL标准训练，确保与SWIM算法的公平对比
    """
    def __init__(self, n_classes=10, resnet_type='resnet18'):
        super(ResNetCifarSWIMBackbone, self).__init__()

        # 根据类型选择ResNet架构（与ResNetCifarWithProjection完全相同）
        if resnet_type == 'resnet18':
            self.backbone = ResNet18(num_classes=n_classes)
        elif resnet_type == 'resnet50':
            self.backbone = ResNet50(num_classes=n_classes)
        else:
            raise ValueError(f"不支持的ResNet类型: {resnet_type}")

        # 获取ResNet的特征维度
        feature_dim = self.backbone.fc.in_features

        # 移除原始的分类层
        self.backbone.fc = nn.Identity()

        # 分类头（与ResNetCifarWithProjection完全相同）
        self.classifier = nn.Linear(feature_dim, n_classes)

    def forward(self, x):
        """
        前向传播（标准分类输出）
        Args:
            x: 输入数据
        Returns:
            classification: 分类结果
        """
        # 特征提取（与ResNetCifarWithProjection完全相同的过程）
        features = self.backbone(x)

        # 分类头输出
        classification = self.classifier(features)

        return classification


class ResNet50CifarSWIMBackbone(nn.Module):
    """
    SWIM ResNet50模型的主干网络（无投影头版本）
    与ResNet50CifarWithProjection使用相同的特征提取器和分类器，但不包含投影头
    用于APPAFL标准训练，确保与SWIM算法的公平对比
    """
    def __init__(self, n_classes=10):
        super(ResNet50CifarSWIMBackbone, self).__init__()

        # 使用ResNet50作为特征提取器（与ResNet50CifarWithProjection完全相同）
        self.backbone = ResNet50(n_classes)

        # 获取ResNet的特征维度
        feature_dim = self.backbone.fc.in_features

        # 移除原始的分类层
        self.backbone.fc = nn.Identity()

        # 分类头（与ResNet50CifarWithProjection完全相同）
        self.classifier = nn.Linear(feature_dim, n_classes)

    def forward(self, x):
        """
        前向传播（标准分类输出）
        Args:
            x: 输入数据
        Returns:
            classification: 分类结果
        """
        # 特征提取（与ResNet50CifarWithProjection完全相同的过程）
        features = self.backbone(x)

        # 分类头输出
        classification = self.classifier(features)

        return classification
