PK                    W  new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/data.pklFB ZZZZZZZ�}q (X   final_accuracyqG@X�p��
=X
   final_lossqG?�ڽ�_��X   accuracy_historyq]q(G@X0     G@X�p��
=eX   configq}q(X   seedqK*X   deviceqX   cpuq	X   datadirq
X   ./dataqX   logdirqX   ./logsq
X   datasetqX   mnistqX	   partitionqX   iidqX   betaqG?ə�����X	   n_clientsqKX	   cfractionqG?�      X   local_epochsqKX   comm_roundsqKX   lrqG?�z�G�{X   train_batch_sizeqK X   test_batch_sizeqK X   modelqX
   simple-cnnqX
   model_versionqX	   optimizedqX   use_swimqKX   out_dimqK@X   temperatureq G?�      X   model_buffer_sizeq!KX   krq"G?�      X   async_weight_strategyq#X   local_roundsq$uu.PK�/�F�  �  PK                    ^ $ new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/.format_versionFB  ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ1PK��܃      PK                    a 0 new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/.storage_alignmentFB, ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                    X 8 new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/byteorderFB4 ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZlittlePK�=�      PK                    V 6 new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/versionFB2 ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ3
PKўgU      PK                    e + new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/.data/serialization_idFB' ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400065788315573363678PK~�#(   (   PK          �/�F�  �  W                 new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/data.pklPK          ��܃      ^                new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/.format_versionPK          ?wq�      a             �  new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/.storage_alignmentPK          �=�      X             �  new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/byteorderPK          ўgU      V             V  new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/versionPK          ~�#(   (   e               new_aggregation_threshold_test_batch_size_32_new_results_mnist_iid_swim1_2_1.0/.data/serialization_idPK,       -                       =      �      PK    5
         PK      =  �    