# 对比实验测试脚本
# 快速验证所有对比实验脚本是否能正常运行
import subprocess
import os
import time
import sys


def test_script(script_name, args, test_name):
    """
    测试单个脚本
    Args:
        script_name: 脚本名称
        args: 命令行参数列表
        test_name: 测试名称
    Returns:
        bool: 测试是否成功
    """
    # 使用当前Python解释器
    cmd = [sys.executable, script_name] + args
    
    print(f"\n{'='*50}")
    print(f"测试: {test_name}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*50}")
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✓ 测试成功，耗时: {end_time - start_time:.2f}秒")
            return True
        else:
            print(f"✗ 测试失败")
            print(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ 测试超时（超过30分钟）")
        return False
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        return False


def main():
    """主函数"""
    print("🧪 开始对比实验测试")
    print("注意：这是快速测试模式，使用较少的轮数和客户端")
    
    # 创建日志目录
    os.makedirs('./logs', exist_ok=True)
    
    # 测试配置
    tests = [
        {
            'script': 'main_fedprox.py',
            'args': ['--dataset', 'cifar10', '--n_clients', '5', '--cfraction', '0.8', 
                    '--local_epochs', '1', '--comm_rounds', '5', '--mu', '0.01'],
            'name': 'FedProx主程序测试'
        },
        {
            'script': 'main_fedasync.py',
            'args': ['--dataset', 'cifar10', '--n_clients', '5', '--cfraction', '0.8', 
                    '--local_epochs', '1', '--comm_rounds', '5', '--alpha', '0.5'],
            'name': 'FedAsync主程序测试'
        },
        {
            'script': 'main.py',
            'args': ['--dataset', 'cifar10', '--n_clients', '5', '--cfraction', '0.8', 
                    '--local_epochs', '1', '--comm_rounds', '5', '--use_swim', '1',
                    '--out_dim', '256', '--temperature', '0.5'],
            'name': '融合算法主程序测试'
        },
        {
            'script': 'compare_with_fedprox.py',
            'args': ['--dataset', 'cifar10', '--quick'],
            'name': '融合算法vs FedProx对比测试'
        },
        {
            'script': 'compare_with_fedasync.py',
            'args': ['--dataset', 'cifar10', '--quick'],
            'name': '融合算法vs FedAsync对比测试'
        },
        {
            'script': 'comprehensive_comparison.py',
            'args': ['--dataset', 'cifar10', '--quick'],
            'name': '三算法综合对比测试'
        }
    ]
    
    # 运行测试
    successful_tests = 0
    failed_tests = 0
    
    for i, test in enumerate(tests, 1):
        print(f"\n🔄 进度: {i}/{len(tests)}")
        
        if test_script(test['script'], test['args'], test['name']):
            successful_tests += 1
        else:
            failed_tests += 1
            
            # 询问是否继续
            if failed_tests == 1:  # 第一次失败时询问
                response = input("\n⚠️  测试失败，是否继续其他测试？(y/n): ")
                if response.lower() != 'y':
                    print("测试中止")
                    break
    
    # 测试总结
    print(f"\n{'='*60}")
    print(f"🧪 测试总结")
    print(f"{'='*60}")
    print(f"总测试数: {len(tests)}")
    print(f"成功: {successful_tests}")
    print(f"失败: {failed_tests}")
    print(f"成功率: {successful_tests/(successful_tests + failed_tests)*100:.1f}%")
    
    if successful_tests == len(tests):
        print(f"\n🎉 所有测试通过！对比实验环境配置正确。")
        print(f"📋 现在可以运行完整的对比实验：")
        print(f"  - python compare_with_fedprox.py --dataset cifar10")
        print(f"  - python compare_with_fedasync.py --dataset cifar10")
        print(f"  - python comprehensive_comparison.py --dataset cifar10")
    elif successful_tests > 0:
        print(f"\n⚠️  部分测试通过，请检查失败的测试。")
    else:
        print(f"\n❌ 所有测试失败，请检查环境配置。")
        print(f"常见问题：")
        print(f"  1. 检查是否安装了所有依赖包")
        print(f"  2. 检查CUDA是否可用（如果使用GPU）")
        print(f"  3. 检查数据集是否正确下载")
        print(f"  4. 检查文件权限")


if __name__ == '__main__':
    main()
