# SWIM-APPAFL集成工具函数
# 包含模型评估、准确率计算等辅助功能
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from sklearn.metrics import confusion_matrix
import logging

# 配置日志
logging.basicConfig()
logger = logging.getLogger()
logger.setLevel(logging.INFO)


def compute_accuracy(model, dataloader, device="cpu"):
    """
    计算模型在给定数据集上的准确率和损失
    
    Args:
        model: 要评估的模型
        dataloader: 数据加载器
        device: 运行设备
    
    Returns:
        tuple: (accuracy, avg_loss) 准确率和平均损失
    """
    # 记录模型原始状态
    was_training = model.training
    model.eval()
    model.to(device)
    
    correct = 0
    total = 0
    total_loss = 0
    
    # 选择损失函数
    criterion = nn.CrossEntropyLoss()
    if device != 'cpu':
        criterion = criterion.to(device)
    
    with torch.no_grad():
        for batch_idx, (data, target) in enumerate(dataloader):
            # 将数据移动到指定设备
            data, target = data.to(device), target.to(device)
            
            # 前向传播
            model_output = model(data)

            # 处理不同类型的模型输出
            if isinstance(model_output, tuple):
                # SWIM模型返回 (features, projection, classification)
                # 评估时我们只需要分类输出
                _, _, output = model_output
            else:
                # 标准模型直接返回分类结果
                output = model_output
            
            # 计算损失
            loss = criterion(output, target)
            total_loss += loss.item()
            
            # 计算准确率
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)
    
    # 恢复模型原始状态
    if was_training:
        model.train()
    
    accuracy = 100.0 * correct / total
    avg_loss = total_loss / len(dataloader)
    
    return accuracy, avg_loss


def compute_loss(model, dataloader, device="cpu"):
    """
    计算模型在给定数据集上的平均损失
    
    Args:
        model: 要评估的模型
        dataloader: 数据加载器
        device: 运行设备
    
    Returns:
        float: 平均损失值
    """
    # 记录模型原始状态
    was_training = model.training
    model.eval()
    model.to(device)
    
    total_loss = 0
    
    # 选择损失函数
    criterion = nn.CrossEntropyLoss()
    if device != 'cpu':
        criterion = criterion.to(device)
    
    with torch.no_grad():
        for batch_idx, (data, target) in enumerate(dataloader):
            # 将数据移动到指定设备
            data, target = data.to(device), target.to(device)
            
            # 前向传播
            model_output = model(data)

            # 处理不同类型的模型输出
            if isinstance(model_output, tuple):
                # SWIM模型返回 (features, projection, classification)
                # 评估时我们只需要分类输出
                _, _, output = model_output
            else:
                # 标准模型直接返回分类结果
                output = model_output
            
            # 计算损失
            loss = criterion(output, target)
            total_loss += loss.item()
    
    # 恢复模型原始状态
    if was_training:
        model.train()
    
    avg_loss = total_loss / len(dataloader)
    return avg_loss


def get_confusion_matrix(model, dataloader, device="cpu"):
    """
    计算混淆矩阵
    
    Args:
        model: 要评估的模型
        dataloader: 数据加载器
        device: 运行设备
    
    Returns:
        numpy.ndarray: 混淆矩阵
    """
    # 记录模型原始状态
    was_training = model.training
    model.eval()
    model.to(device)
    
    true_labels = []
    pred_labels = []
    
    with torch.no_grad():
        for batch_idx, (data, target) in enumerate(dataloader):
            # 将数据移动到指定设备
            data, target = data.to(device), target.to(device)
            
            # 前向传播
            model_output = model(data)

            # 处理不同类型的模型输出
            if isinstance(model_output, tuple):
                # SWIM模型返回 (features, projection, classification)
                # 评估时我们只需要分类输出
                _, _, output = model_output
            else:
                # 标准模型直接返回分类结果
                output = model_output
            
            # 获取预测标签
            pred = output.argmax(dim=1)
            
            # 收集真实标签和预测标签
            true_labels.extend(target.cpu().numpy())
            pred_labels.extend(pred.cpu().numpy())
    
    # 恢复模型原始状态
    if was_training:
        model.train()
    
    # 计算混淆矩阵
    cm = confusion_matrix(true_labels, pred_labels)
    return cm


def save_model(model, filepath):
    """
    保存模型
    
    Args:
        model: 要保存的模型
        filepath: 保存路径
    """
    torch.save(model.state_dict(), filepath)
    print(f"模型已保存到: {filepath}")


def load_model(model, filepath, device="cpu"):
    """
    加载模型
    
    Args:
        model: 模型实例
        filepath: 模型文件路径
        device: 设备
    
    Returns:
        model: 加载后的模型
    """
    model.load_state_dict(torch.load(filepath, map_location=device))
    model.to(device)
    print(f"模型已从 {filepath} 加载")
    return model


def print_model_info(model):
    """
    打印模型信息
    
    Args:
        model: 模型实例
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"模型类型: {model.__class__.__name__}")
    print(f"总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")
    
    # 如果是带投影头的模型，打印更详细的信息
    if hasattr(model, 'projection_head'):
        print("✓ 支持SWIM对比学习")
        if hasattr(model, 'classifier'):
            classifier_params = sum(p.numel() for p in model.classifier.parameters())
            projection_params = sum(p.numel() for p in model.projection_head.parameters())
            print(f"分类头参数: {classifier_params:,}")
            print(f"投影头参数: {projection_params:,}")
    else:
        print("✓ 标准分类模型")


def calculate_model_similarity(model1, model2):
    """
    计算两个模型参数的相似度
    
    Args:
        model1: 模型1
        model2: 模型2
    
    Returns:
        float: 相似度（余弦相似度）
    """
    params1 = []
    params2 = []
    
    # 提取模型参数
    for p1, p2 in zip(model1.parameters(), model2.parameters()):
        params1.append(p1.data.view(-1))
        params2.append(p2.data.view(-1))
    
    # 连接所有参数
    params1 = torch.cat(params1)
    params2 = torch.cat(params2)
    
    # 计算余弦相似度
    similarity = F.cosine_similarity(params1.unsqueeze(0), params2.unsqueeze(0))
    return similarity.item()


def log_training_info(round_num, accuracy, loss, client_info=None):
    """
    记录训练信息
    
    Args:
        round_num: 训练轮次
        accuracy: 准确率
        loss: 损失
        client_info: 客户端信息（可选）
    """
    log_msg = f"Round {round_num}: Accuracy={accuracy:.2f}%, Loss={loss:.4f}"
    
    if client_info:
        log_msg += f", Clients={client_info}"
    
    logger.info(log_msg)
    print(log_msg)


def create_result_summary(results):
    """
    创建结果摘要
    
    Args:
        results: 结果字典
    
    Returns:
        str: 格式化的结果摘要
    """
    summary = "\n=== 实验结果摘要 ===\n"
    
    # 基本配置信息
    config = results.get('config', {})
    summary += f"数据集: {config.get('dataset', 'N/A')}\n"
    summary += f"数据分布: {config.get('partition', 'N/A')}\n"
    summary += f"客户端数量: {config.get('n_clients', 'N/A')}\n"
    summary += f"通信轮数: {config.get('comm_rounds', 'N/A')}\n"
    summary += f"使用SWIM: {'是' if config.get('use_swim', 0) == 1 else '否'}\n"
    
    # 性能结果
    summary += f"\n最终准确率: {results.get('final_accuracy', 0):.2f}%\n"
    summary += f"最终损失: {results.get('final_loss', 0):.4f}\n"
    
    # 准确率历史
    accuracy_history = results.get('accuracy_history', [])
    if accuracy_history:
        max_acc = max(accuracy_history)
        min_acc = min(accuracy_history)
        summary += f"最高准确率: {max_acc:.2f}%\n"
        summary += f"最低准确率: {min_acc:.2f}%\n"
        summary += f"准确率提升: {accuracy_history[-1] - accuracy_history[0]:.2f}%\n"
    
    summary += "=" * 30 + "\n"
    
    return summary
