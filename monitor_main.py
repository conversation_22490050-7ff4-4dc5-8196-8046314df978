#!/usr/bin/env python3
# 实时监控main.py的执行过程
import subprocess
import sys
import time
import threading
import signal
import os

def monitor_process_output(process):
    """实时监控进程输出"""
    print("📺 开始监控输出...")
    
    def read_stdout():
        for line in iter(process.stdout.readline, ''):
            if line:
                print(f"[STDOUT] {line.strip()}")
    
    def read_stderr():
        for line in iter(process.stderr.readline, ''):
            if line:
                print(f"[STDERR] {line.strip()}")
    
    # 创建线程读取输出
    stdout_thread = threading.Thread(target=read_stdout)
    stderr_thread = threading.Thread(target=read_stderr)
    
    stdout_thread.daemon = True
    stderr_thread.daemon = True
    
    stdout_thread.start()
    stderr_thread.start()
    
    return stdout_thread, stderr_thread

def test_main_with_monitoring():
    """使用监控运行main.py"""
    print("🔍 使用实时监控运行main.py")
    print("=" * 60)
    
    # 最简化的参数
    cmd = [
        sys.executable, 'main.py',
        '--seed', '42',
        '--device', 'cpu',  # 强制使用CPU
        '--datadir', './data',
        '--logdir', './logs',
        '--dataset', 'mnist',
        '--partition', 'iid',  # 使用IID，更简单
        '--beta', '0.5',
        '--lr', '0.01',
        '--model', 'simple-cnn',  # 最简单的模型
        '--model_version', 'optimized',
        '--train_batch_size', '32',  # 小批次
        '--test_batch_size', '32',
        '--n_clients', '2',    # 最少客户端
        '--cfraction', '1.0',  # 全部参与
        '--local_epochs', '1', # 最少本地训练
        '--comm_rounds', '2',  # 最少通信轮数
        '--use_swim', '0',     # 不使用SWIM
        '--out_dim', '64',
        '--temperature', '0.5',
        '--model_buffer_size', '2',
        '--kr', '0.5',
        '--async_weight_strategy', 'local_rounds'
    ]
    
    print(f"命令: {' '.join(cmd)}")
    print("⏱️  开始监控（最大等待5分钟）...")
    print("-" * 60)
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 开始监控
        stdout_thread, stderr_thread = monitor_process_output(process)
        
        # 等待进程完成或超时
        start_time = time.time()
        timeout = 300  # 5分钟超时
        
        while True:
            # 检查进程是否结束
            if process.poll() is not None:
                print("\n✅ 进程正常结束")
                break
                
            # 检查超时
            elapsed = time.time() - start_time
            if elapsed > timeout:
                print(f"\n⏰ 超时 ({timeout}秒)，终止进程...")
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    print("强制终止进程...")
                    process.kill()
                    process.wait()
                return False, "超时"
            
            # 每10秒显示一次状态
            if int(elapsed) % 10 == 0 and int(elapsed) > 0:
                print(f"⏱️  运行时间: {int(elapsed)}秒")
            
            time.sleep(1)
        
        # 获取最终结果
        return_code = process.returncode
        
        if return_code == 0:
            print("🎉 main.py执行成功！")
            return True, "成功"
        else:
            print(f"❌ main.py执行失败，返回码: {return_code}")
            return False, f"返回码: {return_code}"
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
        if 'process' in locals():
            process.terminate()
        return False, "用户中断"
    except Exception as e:
        print(f"❌ 监控异常: {e}")
        return False, str(e)

def test_simple_import():
    """测试简单的导入"""
    print("🧪 测试简单导入...")
    
    try:
        # 测试导入main.py
        print("  导入main.py...")
        import main
        print("  ✅ main.py导入成功")
        
        # 测试获取参数
        print("  测试参数解析...")
        old_argv = sys.argv
        sys.argv = ['main.py', '--help']
        try:
            main.get_args()
        except SystemExit:
            pass  # help会导致SystemExit，这是正常的
        sys.argv = old_argv
        print("  ✅ 参数解析正常")
        
        return True
    except Exception as e:
        print(f"  ❌ 导入测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 main.py执行监控器")
    print("=" * 60)
    
    # 检查工作目录
    print(f"📍 当前目录: {os.getcwd()}")
    print(f"📁 main.py存在: {os.path.exists('main.py')}")
    print(f"📁 data目录存在: {os.path.exists('./data')}")
    
    # 测试1: 简单导入
    print("\n🧪 步骤1: 测试导入")
    if not test_simple_import():
        print("❌ 导入测试失败，请检查代码")
        return
    
    # 测试2: 监控执行
    print("\n🔍 步骤2: 监控执行")
    success, message = test_main_with_monitoring()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 main.py可以正常运行！")
        print("💡 现在可以尝试运行对比实验")
    else:
        print("❌ main.py运行失败")
        print(f"原因: {message}")
        print("\n💡 可能的解决方案:")
        print("  1. 检查数据集是否完整")
        print("  2. 尝试更小的参数设置")
        print("  3. 检查内存使用情况")
        print("  4. 查看上面的错误输出")

if __name__ == '__main__':
    main()
