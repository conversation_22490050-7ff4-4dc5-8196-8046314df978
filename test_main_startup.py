#!/usr/bin/env python3
# 测试main.py的启动过程，逐步执行并显示进度
import sys
import os

def test_main_startup():
    """逐步测试main.py的启动过程"""
    print("🚀 测试main.py启动过程")
    print("=" * 50)
    
    try:
        print("📍 步骤1: 导入基础模块...")
        import argparse
        import torch
        import numpy as np
        import random
        import copy
        print("  ✅ 基础模块导入成功")
        
        print("📍 步骤2: 设置CUDA环境...")
        os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
        print("  ✅ CUDA环境设置完成")
        
        print("📍 步骤3: 导入项目模块...")
        print("  导入models...")
        from models import CNNCifar, CNNCifarWithProjection, CNNMnist, CNNMnistWithProjection, SimpleCNNMnist, SimpleCNNMnistWithProjection
        print("  ✅ models模块")
        
        print("  导入swim_models...")
        from swim_models import ModelFedCon_SWIM, ModelFedCon_SWIM_NoHeader
        print("  ✅ swim_models模块")
        
        print("  导入clients...")
        from clients import ClientsGroup
        print("  ✅ clients模块")
        
        print("  导入server...")
        from server import APPAFLServer
        print("  ✅ server模块")
        
        print("📍 步骤4: 模拟main.py的参数解析...")
        # 模拟命令行参数
        sys.argv = [
            'main.py',
            '--seed', '42',
            '--device', 'cpu',  # 使用CPU避免GPU问题
            '--datadir', './data',
            '--logdir', './logs',
            '--dataset', 'mnist',
            '--partition', 'iid',  # 使用IID简化
            '--beta', '0.5',
            '--lr', '0.01',
            '--model', 'simple-cnn',
            '--model_version', 'optimized',
            '--train_batch_size', '32',
            '--test_batch_size', '32',
            '--n_clients', '2',
            '--cfraction', '1.0',
            '--local_epochs', '1',
            '--comm_rounds', '2',
            '--use_swim', '0',  # 不使用SWIM
            '--out_dim', '64',
            '--temperature', '0.5',
            '--model_buffer_size', '2',
            '--kr', '0.5',
            '--async_weight_strategy', 'local_rounds'
        ]
        
        # 导入main.py的函数
        print("📍 步骤5: 导入main.py的函数...")
        from main import get_args, set_seed
        print("  ✅ main.py函数导入成功")
        
        print("📍 步骤6: 解析参数...")
        args = get_args()
        print(f"  ✅ 参数解析成功: dataset={args.dataset}, model={args.model}")
        
        print("📍 步骤7: 设置随机种子...")
        set_seed(args.seed)
        print("  ✅ 随机种子设置完成")
        
        print("📍 步骤8: 创建目录...")
        os.makedirs(args.datadir, exist_ok=True)
        os.makedirs(args.logdir, exist_ok=True)
        print("  ✅ 目录创建完成")
        
        print("📍 步骤9: 测试模型创建...")
        from models import get_model
        model = get_model(
            model_type=args.model,
            dataset=args.dataset,
            use_swim=args.use_swim,
            out_dim=args.out_dim,
            model_version=args.model_version,
            verbose=True
        )
        print(f"  ✅ 模型创建成功: {model.__class__.__name__}")
        
        print("📍 步骤10: 测试客户端组创建...")
        print("  开始创建客户端组（这一步可能需要时间）...")
        clients_group = ClientsGroup(
            dataset=args.dataset,
            datadir=args.datadir,
            partition=args.partition,
            n_clients=args.n_clients,
            beta=args.beta,
            train_batch_size=args.train_batch_size,
            test_batch_size=args.test_batch_size,
            device=args.device
        )
        print(f"  ✅ 客户端组创建成功: {args.n_clients}个客户端")
        
        print("📍 步骤11: 测试服务器创建...")
        server = APPAFLServer(model, args.device)
        print("  ✅ 服务器创建成功")
        
        print("\n🎉 main.py启动测试完全成功！")
        print("💡 main.py应该可以正常运行")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_main_startup()
    
    if success:
        print("\n" + "=" * 50)
        print("✅ 诊断结果: main.py启动流程正常")
        print("💡 如果main.py仍然卡住，可能的原因:")
        print("   1. 数据加载时间较长（即使有数据集）")
        print("   2. GPU内存不足或初始化慢")
        print("   3. 模型参数设置导致计算量大")
        print("\n💡 建议:")
        print("   1. 尝试使用CPU: --device cpu")
        print("   2. 减少客户端数量: --n_clients 2")
        print("   3. 减少通信轮数: --comm_rounds 5")
        print("   4. 使用IID分布: --partition iid")
    else:
        print("\n" + "=" * 50)
        print("❌ 发现问题，请根据上述错误信息修复")

if __name__ == '__main__':
    main()
