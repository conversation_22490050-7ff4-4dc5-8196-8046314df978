#!/usr/bin/env python3
# 调试main.py的启动问题
import sys
import os

def test_imports():
    """测试所有必要的导入"""
    print("🔍 测试模块导入...")
    
    try:
        print("  导入基础模块...")
        import torch
        import numpy as np
        import argparse
        print(f"  ✅ PyTorch: {torch.__version__}")
        
        print("  导入项目模块...")
        from models import CNNCifar, CNNCifarWithProjection, CNNMnist, CNNMnistWithProjection
        print("  ✅ models模块")
        
        from swim_models import ModelFedCon_SWIM, ModelFedCon_SWIM_NoHeader
        print("  ✅ swim_models模块")
        
        from clients import ClientsGroup
        print("  ✅ clients模块")
        
        from server import APPAFLServer
        print("  ✅ server模块")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n🔧 测试模型创建...")
    
    try:
        from models import get_model
        
        # 测试创建简单模型
        model = get_model(
            model_type='simple-cnn',
            dataset='mnist',
            use_swim=0,  # 不使用SWIM，简化测试
            out_dim=64,
            model_version='optimized',
            verbose=True
        )
        print(f"  ✅ 模型创建成功: {model.__class__.__name__}")
        return True
        
    except Exception as e:
        print(f"  ❌ 模型创建失败: {e}")
        return False

def test_data_loading():
    """测试数据加载"""
    print("\n📊 测试数据加载...")
    
    try:
        from datasets import partition_data
        
        # 测试数据分割
        print("  测试数据分割...")
        X_train, y_train, X_test, y_test, net_dataidx_map, traindata_cls_counts = partition_data(
            'mnist', './data', './logs', 'iid', 2, beta=0.5
        )
        print(f"  ✅ 数据分割成功: 训练集{len(X_train)}, 测试集{len(X_test)}")
        return True
        
    except Exception as e:
        print(f"  ❌ 数据加载失败: {e}")
        return False

def test_minimal_training():
    """测试最小化训练流程"""
    print("\n🏃 测试最小化训练流程...")
    
    try:
        # 设置参数
        class Args:
            seed = 42
            device = 'cpu'  # 强制使用CPU
            dataset = 'mnist'
            partition = 'iid'
            beta = 0.5
            n_clients = 2
            cfraction = 1.0
            local_epochs = 1
            comm_rounds = 1
            lr = 0.01
            model = 'simple-cnn'
            model_version = 'optimized'
            train_batch_size = 32
            test_batch_size = 32
            use_swim = 0  # 不使用SWIM
            out_dim = 64
            temperature = 0.5
            model_buffer_size = 2
            kr = 0.5
            datadir = './data'
            logdir = './logs'
            async_weight_strategy = 'local_rounds'
        
        args = Args()
        
        # 创建目录
        os.makedirs(args.datadir, exist_ok=True)
        os.makedirs(args.logdir, exist_ok=True)
        
        # 设置随机种子
        import torch
        import numpy as np
        import random
        torch.manual_seed(args.seed)
        np.random.seed(args.seed)
        random.seed(args.seed)
        
        print(f"  ✅ 参数设置完成")
        
        # 创建模型
        from models import get_model
        model = get_model(
            model_type=args.model,
            dataset=args.dataset,
            use_swim=args.use_swim,
            out_dim=args.out_dim,
            model_version=args.model_version,
            verbose=False
        )
        print(f"  ✅ 模型创建完成: {model.__class__.__name__}")
        
        # 创建客户端组
        from clients import ClientsGroup
        print("  创建客户端组...")
        clients_group = ClientsGroup(
            dataset=args.dataset,
            datadir=args.datadir,
            partition=args.partition,
            n_clients=args.n_clients,
            beta=args.beta,
            train_batch_size=args.train_batch_size,
            test_batch_size=args.test_batch_size,
            device=args.device
        )
        print(f"  ✅ 客户端组创建完成")
        
        # 创建服务器
        from server import APPAFLServer
        print("  创建服务器...")
        server = APPAFLServer(model, args.device)
        print(f"  ✅ 服务器创建完成")
        
        # 测试一轮训练
        print("  测试一轮训练...")
        selected_clients = clients_group.select_clients(args.cfraction)
        print(f"  选择了 {len(selected_clients)} 个客户端")
        
        # 简单测试完成
        print("  ✅ 最小化训练流程测试成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 训练流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 调试main.py启动问题")
    print("=" * 60)
    
    # 测试1: 模块导入
    if not test_imports():
        print("\n❌ 模块导入失败，请检查环境")
        return
    
    # 测试2: 模型创建
    if not test_model_creation():
        print("\n❌ 模型创建失败")
        return
    
    # 测试3: 数据加载
    if not test_data_loading():
        print("\n❌ 数据加载失败")
        return
    
    # 测试4: 最小化训练
    if not test_minimal_training():
        print("\n❌ 训练流程失败")
        return
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("💡 main.py应该可以正常运行")
    print("💡 如果main.py仍然卡住，可能是:")
    print("   1. 数据下载需要时间")
    print("   2. GPU初始化需要时间")
    print("   3. 模型太复杂，建议使用CPU测试")

if __name__ == '__main__':
    main()
