#!/usr/bin/env python3
# 简单测试脚本 - 验证main.py是否能正常启动
import subprocess
import sys
import time
import signal
import os

def test_main_script():
    """测试main.py是否能正常启动"""
    print("🧪 测试main.py启动...")
    
    # 创建必要的目录
    os.makedirs('./data', exist_ok=True)
    os.makedirs('./logs', exist_ok=True)
    
    # 最简化的参数
    cmd = [
        sys.executable, 'main.py',
        '--seed', '42',
        '--device', 'cpu',  # 强制使用CPU
        '--datadir', './data',
        '--logdir', './logs',
        '--dataset', 'mnist',  # 最简单的数据集
        '--partition', 'iid',  # IID分布
        '--n_clients', '2',    # 最少客户端
        '--cfraction', '1.0',  # 全部参与
        '--local_epochs', '1', # 最少本地训练
        '--comm_rounds', '2',  # 最少通信轮数
        '--lr', '0.01',
        '--model', 'simple-cnn',
        '--model_version', 'optimized',
        '--train_batch_size', '32',
        '--test_batch_size', '32',
        '--use_swim', '0',     # 不使用SWIM，简化测试
        '--out_dim', '64',
        '--temperature', '0.5',
        '--model_buffer_size', '2',
        '--kr', '0.5'
    ]
    
    print(f"命令: {' '.join(cmd)}")
    print("⏱️  开始测试（最大等待2分钟）...")
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时监控
        start_time = time.time()
        timeout = 120  # 2分钟超时
        output_lines = []
        
        while True:
            # 检查进程是否结束
            if process.poll() is not None:
                print("✅ 进程正常结束")
                break
                
            # 检查超时
            elapsed = time.time() - start_time
            if elapsed > timeout:
                print("⏰ 测试超时，终止进程...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
                return False, "超时"
            
            # 读取输出
            try:
                output = process.stdout.readline()
                if output:
                    line = output.strip()
                    print(f"📝 {line}")
                    output_lines.append(line)
                    
                    # 检查是否有错误信息
                    if "error" in line.lower() or "exception" in line.lower():
                        print("❌ 检测到错误")
                        process.terminate()
                        return False, f"错误: {line}"
                    
                    # 检查是否成功启动
                    if "创建客户端组" in line or "开始联邦学习训练" in line:
                        print("✅ 成功启动！")
                        # 让它运行一小段时间后终止
                        time.sleep(10)
                        process.terminate()
                        return True, "成功启动"
                        
            except Exception as e:
                print(f"读取输出时出错: {e}")
            
            time.sleep(0.1)
        
        # 获取最终结果
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            print("✅ 测试成功！")
            return True, "完成"
        else:
            print("❌ 测试失败")
            print(f"错误输出: {stderr}")
            return False, stderr
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False, str(e)

def main():
    """主函数"""
    print("🚀 简单启动测试")
    print("=" * 50)
    
    success, message = test_main_script()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试成功！main.py可以正常启动")
        print("💡 现在可以尝试运行对比实验：")
        print("  python compare_with_fedasync.py --dataset mnist --quick")
    else:
        print("❌ 测试失败")
        print(f"原因: {message}")
        print("💡 建议：")
        print("  1. 检查所有依赖是否正确安装")
        print("  2. 检查数据集下载是否正常")
        print("  3. 尝试使用更简单的参数")

if __name__ == '__main__':
    main()
