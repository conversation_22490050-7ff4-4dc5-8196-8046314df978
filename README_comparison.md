# 融合算法与FedProx、FedAsync对比实验指南

本文档介绍如何运行融合算法（SWIM-APPAFL）与FedProx、FedAsync算法的对比实验。

## 文件说明

### 主程序文件
- `main.py` - 原始融合算法（SWIM-APPAFL）主程序
- `main_fedprox.py` - FedProx算法主程序
- `main_fedasync.py` - FedAsync算法主程序

### 对比实验脚本
- `compare_with_fedprox.py` - 融合算法与FedProx的对比实验
- `compare_with_fedasync.py` - 融合算法与FedAsync的对比实验
- `comprehensive_comparison.py` - 三种算法的综合对比实验

## 快速开始

### 1. 融合算法 vs FedProx 对比

```bash
# 快速测试模式（CIFAR-10数据集）
python compare_with_fedprox.py --dataset cifar10 --quick

# 完整实验模式（CIFAR-10数据集）
python compare_with_fedprox.py --dataset cifar10

# 使用不同的FedProx正则化参数
python compare_with_fedprox.py --dataset cifar10 --mu 0.1
```

### 2. 融合算法 vs FedAsync 对比

```bash
# 快速测试模式（CIFAR-10数据集）
python compare_with_fedasync.py --dataset cifar10 --quick

# 完整实验模式（CIFAR-10数据集）
python compare_with_fedasync.py --dataset cifar10

# 使用不同的FedAsync权重参数
python compare_with_fedasync.py --dataset cifar10 --alpha 0.3
```

### 3. 三种算法综合对比

```bash
# 快速测试模式
python comprehensive_comparison.py --dataset cifar10 --quick

# 完整实验模式
python comprehensive_comparison.py --dataset cifar10

# 使用不同数据集
python comprehensive_comparison.py --dataset cifar100
python comprehensive_comparison.py --dataset mnist
```

## 参数说明

### 通用参数
- `--dataset`: 数据集选择 (cifar10, cifar100, mnist, fashion_mnist)
- `--quick`: 快速测试模式（减少轮数和客户端数量）

### FedProx特有参数
- `--mu`: 正则化参数，默认0.01

### FedAsync特有参数
- `--alpha`: 异步聚合权重参数，默认0.5

## 实验配置

### 快速测试模式 (--quick)
- 客户端数量: 10
- 参与比例: 0.5
- 本地训练轮数: 2
- 通信轮数: 20

### 完整实验模式
- 客户端数量: 100
- 参与比例: 0.1
- 本地训练轮数: 10
- 通信轮数: 100

### 其他固定参数
- 数据分布: noniid (beta=0.5)
- 学习率: 0.01
- 模型: CNN
- 批次大小: 64
- 随机种子: 42

## 结果文件

实验结果将保存在 `./logs/` 目录下：

### 单算法结果文件
- `results_{dataset}_{partition}_swim1.pt` - 融合算法结果
- `fedprox_{dataset}_{partition}_mu{mu}.pt` - FedProx结果
- `fedasync_{dataset}_{partition}_alpha{alpha}.pt` - FedAsync结果

### 对比结果文件
- `comparison_fusion_vs_fedprox_{dataset}.pt` - 融合算法vs FedProx对比
- `comparison_fusion_vs_fedasync_{dataset}.pt` - 融合算法vs FedAsync对比
- `comprehensive_comparison_{dataset}.pt` - 三算法综合对比

### 可视化文件
- `*_comparison.png` - 对比图表
- `*_comprehensive_comparison.png` - 综合对比图表

## 结果分析

每个对比实验都会生成：

1. **准确率对比**: 最终测试准确率的柱状图对比
2. **训练时间对比**: 总训练时间的对比
3. **收敛曲线**: 训练过程中准确率的变化曲线
4. **综合性能雷达图**: 多维度性能对比（仅综合对比）

## 示例输出

```
📈 实验总结:
  准确率更优: SWIM-APPAFL
  训练更快: FedProx
  准确率提升: +0.0234 (+2.45%)
  时间比率: 1.15x
```

## 注意事项

1. **GPU内存**: 确保有足够的GPU内存运行实验
2. **时间消耗**: 完整实验模式可能需要较长时间
3. **参数一致性**: 所有对比实验都使用相同的基础参数，只有算法特有参数不同
4. **结果可重现**: 使用固定随机种子确保结果可重现

## 故障排除

### 常见问题
1. **CUDA内存不足**: 使用 `--quick` 模式或减少批次大小
2. **实验超时**: 单个实验最大运行时间为2小时
3. **文件权限**: 确保对 `./logs/` 目录有写权限

### 调试建议
1. 先运行快速测试模式验证环境
2. 检查数据集是否正确下载到 `./data/` 目录
3. 确认所有依赖包已正确安装

## 扩展实验

可以通过修改脚本中的参数进行更多对比实验：

1. **不同数据分布**: 修改 `beta` 参数
2. **不同客户端数量**: 修改 `n_clients` 参数
3. **不同学习率**: 修改 `lr` 参数
4. **不同模型架构**: 修改 `model` 参数

## 联系方式

如有问题，请检查代码注释或联系开发团队。
