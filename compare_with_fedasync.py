# 融合算法与FedAsync对比实验脚本
# 自动运行融合算法(SWIM-APPAFL)和FedAsync的对比实验，确保参数一致性
import subprocess
import os
import time
import argparse
import torch
import matplotlib.pyplot as plt
import numpy as np
import sys


def run_experiment(script_name, config, experiment_name):
    """
    运行单个实验
    Args:
        script_name: 脚本名称 (main.py 或 main_fedasync.py)
        config: 实验配置字典
        experiment_name: 实验名称
    Returns:
        tuple: (是否成功, 结果文件路径)
    """
    # 构建命令行参数
    cmd = [sys.executable, script_name]
    for key, value in config.items():
        if key != 'experiment_name':  # 排除实验名称参数
            cmd.extend([f'--{key}', str(value)])
    
    print(f"\n{'='*60}")
    print(f"运行实验: {experiment_name}")
    print(f"脚本: {script_name}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        # 运行实验
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=7200)  # 2小时超时
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✓ 实验成功完成，耗时: {end_time - start_time:.2f}秒")
            
            # 确定结果文件路径
            if 'fedasync' in script_name:
                result_file = f"fedasync_{config['dataset']}_{config['partition']}_alpha{config.get('alpha', 0.5)}.pt"
            else:
                swim_flag = config.get('use_swim', 0)
                result_file = f"results_{config['dataset']}_{config['partition']}_swim{swim_flag}.pt"
            
            result_path = os.path.join(config['logdir'], result_file)
            return True, result_path
        else:
            print(f"✗ 实验失败")
            print(f"错误输出: {result.stderr}")
            return False, None
            
    except subprocess.TimeoutExpired:
        print(f"✗ 实验超时（超过2小时）")
        return False, None
    except Exception as e:
        print(f"✗ 实验异常: {e}")
        return False, None


def load_and_compare_results(fusion_result_path, fedasync_result_path, save_path):
    """
    加载并对比实验结果
    Args:
        fusion_result_path: 融合算法结果文件路径
        fedasync_result_path: FedAsync结果文件路径
        save_path: 对比结果保存路径
    """
    try:
        # 加载结果
        fusion_results = torch.load(fusion_result_path)
        fedasync_results = torch.load(fedasync_result_path)
        
        print(f"\n{'='*60}")
        print(f"实验结果对比")
        print(f"{'='*60}")
        
        # 提取关键指标
        fusion_accuracy = fusion_results['final_accuracy']
        fedasync_accuracy = fedasync_results['final_accuracy']
        fusion_time = fusion_results['total_time']
        fedasync_time = fedasync_results['total_time']
        
        print(f"融合算法(SWIM-APPAFL):")
        print(f"  最终准确率: {fusion_accuracy:.4f}")
        print(f"  训练时间: {fusion_time:.2f}秒")
        
        print(f"FedAsync:")
        print(f"  最终准确率: {fedasync_accuracy:.4f}")
        print(f"  训练时间: {fedasync_time:.2f}秒")
        
        # 计算改进
        accuracy_improvement = fusion_accuracy - fedasync_accuracy
        time_ratio = fusion_time / fedasync_time
        
        print(f"\n对比结果:")
        print(f"  准确率改进: {accuracy_improvement:+.4f} ({accuracy_improvement/fedasync_accuracy*100:+.2f}%)")
        print(f"  时间比率: {time_ratio:.2f}x")
        
        # 绘制对比图
        plt.figure(figsize=(15, 5))
        
        # 准确率对比
        plt.subplot(1, 3, 1)
        algorithms = ['FedAsync', 'SWIM-APPAFL\n(融合算法)']
        accuracies = [fedasync_accuracy, fusion_accuracy]
        colors = ['lightgreen', 'lightcoral']
        bars = plt.bar(algorithms, accuracies, color=colors)
        plt.ylabel('最终准确率')
        plt.title('准确率对比')
        plt.ylim(0, 1)
        
        # 在柱状图上添加数值
        for bar, acc in zip(bars, accuracies):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{acc:.4f}', ha='center', va='bottom')
        
        # 训练时间对比
        plt.subplot(1, 3, 2)
        times = [fedasync_time, fusion_time]
        bars = plt.bar(algorithms, times, color=colors)
        plt.ylabel('训练时间 (秒)')
        plt.title('训练时间对比')
        
        # 在柱状图上添加数值
        for bar, t in zip(bars, times):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(times)*0.01, 
                    f'{t:.1f}s', ha='center', va='bottom')
        
        # 准确率历史对比
        plt.subplot(1, 3, 3)
        if 'accuracy_history' in fusion_results and 'accuracy_history' in fedasync_results:
            fusion_history = fusion_results['accuracy_history']
            fedasync_history = fedasync_results['accuracy_history']
            
            # 确保两个历史记录长度一致
            min_len = min(len(fusion_history), len(fedasync_history))
            if min_len > 0:
                rounds = list(range(10, min_len * 10 + 1, 10))  # 假设每10轮记录一次
                plt.plot(rounds[:min_len], fedasync_history[:min_len], 'o-', label='FedAsync', color='lightgreen')
                plt.plot(rounds[:min_len], fusion_history[:min_len], 's-', label='SWIM-APPAFL', color='lightcoral')
                plt.xlabel('通信轮数')
                plt.ylabel('测试准确率')
                plt.title('准确率收敛曲线')
                plt.legend()
                plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存对比图
        plot_path = save_path.replace('.pt', '_comparison.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        print(f"✓ 对比图已保存到: {plot_path}")
        plt.close()
        
        # 保存对比结果
        comparison_results = {
            'fusion_algorithm': {
                'name': 'SWIM-APPAFL',
                'final_accuracy': fusion_accuracy,
                'training_time': fusion_time,
                'accuracy_history': fusion_results.get('accuracy_history', []),
                'config': fusion_results['config']
            },
            'fedasync_algorithm': {
                'name': 'FedAsync',
                'final_accuracy': fedasync_accuracy,
                'training_time': fedasync_time,
                'accuracy_history': fedasync_results.get('accuracy_history', []),
                'config': fedasync_results['config']
            },
            'comparison': {
                'accuracy_improvement': accuracy_improvement,
                'accuracy_improvement_percentage': accuracy_improvement/fedasync_accuracy*100,
                'time_ratio': time_ratio,
                'better_accuracy': 'SWIM-APPAFL' if fusion_accuracy > fedasync_accuracy else 'FedAsync',
                'faster_training': 'SWIM-APPAFL' if fusion_time < fedasync_time else 'FedAsync'
            }
        }
        
        torch.save(comparison_results, save_path)
        print(f"✓ 对比结果已保存到: {save_path}")
        
        return comparison_results
        
    except Exception as e:
        print(f"✗ 结果对比失败: {e}")
        return None


def main():
    """主函数"""
    print("🚀 开始融合算法与FedAsync对比实验")
    print("📍 当前工作目录:", os.getcwd())

    parser = argparse.ArgumentParser(description='融合算法与FedAsync对比实验')
    parser.add_argument('--dataset', type=str, default='mnist', choices=['cifar10', 'cifar100', 'mnist', 'fashion_mnist'],
                        help='数据集选择')
    parser.add_argument('--quick', action='store_true', default=True, help='快速测试模式（减少轮数和客户端）')
    parser.add_argument('--alpha', type=float, default=0.5, help='FedAsync异步聚合权重参数')
    args = parser.parse_args()
    
    # 基础配置（确保两个算法使用完全相同的参数）
    base_config = {
        'seed': 42,
        'device': 'cuda',
        'datadir': './data',
        'logdir': './logs',
        'dataset': args.dataset,
        'partition': 'noniid',
        'beta': 0.5,
        'lr': 0.01,
        'model': 'cnn',
        'model_version': 'optimized',
        'train_batch_size': 64,
        'test_batch_size': 64
    }
    
    # 根据模式调整参数
    if args.quick:
        base_config.update({
            'n_clients': 10,
            'cfraction': 0.5,
            'local_epochs': 2,
            'comm_rounds': 20,
        })
        print("🚀 快速测试模式")
    else:
        base_config.update({
            'n_clients': 100,
            'cfraction': 0.1,
            'local_epochs': 10,
            'comm_rounds': 100,
        })
        print("🔬 完整实验模式")
    
    # 创建日志目录
    os.makedirs(base_config['logdir'], exist_ok=True)
    
    print(f"\n📋 开始融合算法与FedAsync对比实验")
    print(f"数据集: {args.dataset}")
    print(f"FedAsync权重参数: {args.alpha}")
    
    # 实验1: 运行融合算法 (SWIM-APPAFL)
    fusion_config = base_config.copy()
    fusion_config.update({
        'use_swim': 1,
        'out_dim': 256,
        'temperature': 0.5,
        'model_buffer_size': 4,
        'kr': 0.5,
        'async_weight_strategy': 'local_rounds'
    })
    
    print(f"\n🔄 步骤 1/2: 运行融合算法 (SWIM-APPAFL)")
    fusion_success, fusion_result_path = run_experiment('main.py', fusion_config, 'SWIM-APPAFL融合算法')
    
    if not fusion_success:
        print("✗ 融合算法实验失败，终止对比")
        return
    
    # 实验2: 运行FedAsync
    fedasync_config = base_config.copy()
    fedasync_config.update({
        'alpha': args.alpha,
        'max_staleness': 5
    })
    
    print(f"\n🔄 步骤 2/2: 运行FedAsync算法")
    fedasync_success, fedasync_result_path = run_experiment('main_fedasync.py', fedasync_config, 'FedAsync算法')
    
    if not fedasync_success:
        print("✗ FedAsync实验失败，终止对比")
        return
    
    # 对比结果
    print(f"\n🔄 步骤 3/3: 对比实验结果")
    comparison_save_path = os.path.join(base_config['logdir'], f'comparison_fusion_vs_fedasync_{args.dataset}.pt')
    comparison_results = load_and_compare_results(fusion_result_path, fedasync_result_path, comparison_save_path)
    
    if comparison_results:
        print(f"\n🎉 对比实验完成！")
        print(f"📊 详细结果请查看: {comparison_save_path}")
        
        # 输出简要总结
        comp = comparison_results['comparison']
        print(f"\n📈 实验总结:")
        print(f"  准确率更优: {comp['better_accuracy']}")
        print(f"  训练更快: {comp['faster_training']}")
        print(f"  准确率提升: {comp['accuracy_improvement']:+.4f} ({comp['accuracy_improvement_percentage']:+.2f}%)")
        print(f"  时间比率: {comp['time_ratio']:.2f}x")
    else:
        print("✗ 结果对比失败")


if __name__ == '__main__':
    main()
