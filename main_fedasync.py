# FedAsync异步联邦学习主程序
# 实现FedAsync算法的完整训练流程，用于与融合算法进行对比
import argparse
import os
import torch
import numpy as np
import random
import copy
import time

# 设置CUDA环境变量以便更好的错误调试
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
from models import CNNCifar, CNNCifarWithProjection, ResNetCifarWithProjection, CNNCifarSWIMBackbone, ResNetCifarSWIMBackbone, ResNet50CifarWithProjection, ResNet50CifarSWIMBackbone, CNNMnist, CNNMnistWithProjection, SimpleCNNMnist, SimpleCNNMnistWithProjection
from swim_models import ModelFedCon_SWIM, ModelFedCon_SWIM_NoHeader
from clients import ClientsGroup
from server import FedAsyncServer
from utils import test_model


def set_seed(seed):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def get_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='FedAsync异步联邦学习')
    # 基本参数
    parser.add_argument('--seed', type=int, default=0, help='随机种子')
    parser.add_argument('--device', type=str, default='cuda', help='计算设备')
    parser.add_argument('--datadir', type=str, default='./data', help='数据目录')
    parser.add_argument('--logdir', type=str, default='./logs', help='日志目录')

    # 数据集参数
    parser.add_argument('--dataset', type=str, default='cifar10', choices=['cifar10', 'cifar100', 'mnist', 'fashion_mnist'],
                        help='数据集选择')
    parser.add_argument('--partition', type=str, default='noniid', choices=['iid', 'noniid'],
                        help='数据分布类型')
    parser.add_argument('--beta', type=float, default=0.2, help='非IID数据分布参数')

    # 联邦学习参数
    parser.add_argument('--n_clients', type=int, default=100, help='客户端数量')
    parser.add_argument('--cfraction', type=float, default=0.1, help='每轮参与的客户端比例')
    parser.add_argument('--local_epochs', type=int, default=10, help='本地训练轮数')
    parser.add_argument('--comm_rounds', type=int, default=100, help='通信轮数')
    parser.add_argument('--lr', type=float, default=0.01, help='学习率')

    # 数据加载参数
    parser.add_argument('--train_batch_size', type=int, default=64, help='训练批次大小')
    parser.add_argument('--test_batch_size', type=int, default=64, help='测试批次大小')

    # 模型参数
    parser.add_argument('--model', type=str, default='cnn', choices=['cnn', 'resnet18', 'resnet50'],
                        help='模型类型')
    parser.add_argument('--model_version', type=str, default='v1', choices=['v1', 'v2'],
                        help='模型版本')

    # FedAsync特有参数
    parser.add_argument('--alpha', type=float, default=0.5, help='FedAsync异步聚合权重参数')
    parser.add_argument('--max_staleness', type=int, default=5, help='最大延迟轮数')

    # 实验参数
    parser.add_argument('--experiment_name', type=str, default='fedasync_experiment', help='实验名称')
    parser.add_argument('--save_results', type=int, default=1, help='是否保存结果')

    return parser.parse_args()


def create_model(args, verbose=True):
    """创建模型"""
    # 确定类别数
    if args.dataset == 'cifar10':
        n_classes = 10
    elif args.dataset == 'cifar100':
        n_classes = 100
    elif args.dataset in ['mnist', 'fashion_mnist']:
        n_classes = 10
    else:
        raise ValueError(f"不支持的数据集: {args.dataset}")

    # 确定基础模型类型
    if args.model == 'resnet50':
        if args.dataset == 'cifar100':
            base_model = "resnet50-cifar100"
        elif args.dataset in ['mnist', 'fashion_mnist']:
            base_model = "resnet50-mnist"
        else:
            base_model = "resnet50-cifar10"  # CIFAR-10使用ResNet50
    else:
        # 默认使用ResNet18
        if args.dataset == 'cifar100':
            base_model = "resnet18-cifar100"
        elif args.dataset in ['mnist', 'fashion_mnist']:
            base_model = "resnet18-mnist"
        else:
            base_model = "resnet18"

    # FedAsync使用无投影头的模型（与SWIM架构保持一致但无投影头）
    model = ModelFedCon_SWIM_NoHeader(
        base_model=base_model,
        out_dim=256,  # 保持与SWIM一致的参数
        n_classes=n_classes
    )
    if verbose:
        print(f"✓ 创建FedAsync模型（无投影头）: {model.__class__.__name__}, 基础模型: {base_model}")

    return model


def main():
    """主函数"""
    # 解析参数
    args = get_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建日志目录
    os.makedirs(args.logdir, exist_ok=True)
    
    # 打印配置信息
    print("\n=== FedAsync异步联邦学习配置 ===")
    print(f"数据集: {args.dataset}")
    print(f"数据分布: {args.partition} (beta={args.beta})")
    print(f"客户端数量: {args.n_clients}")
    print(f"每轮参与比例: {args.cfraction}")
    print(f"本地训练轮数: {args.local_epochs}")
    print(f"通信轮数: {args.comm_rounds}")
    print(f"学习率: {args.lr}")
    print(f"FedAsync权重参数: {args.alpha}")
    print(f"最大延迟轮数: {args.max_staleness}")
    print(f"模型类型: {args.model}")
    print(f"模型版本: {args.model_version}")
    print(f"训练批次大小: {args.train_batch_size}")
    print(f"测试批次大小: {args.test_batch_size}")

    print(f"批次大小配置:")
    print(f"- 训练批次大小: {args.train_batch_size}")
    print(f"- 测试批次大小: {args.test_batch_size}")
    
    # 创建模型
    model = create_model(args)
    
    # 创建客户端组
    print("\n=== 创建客户端组 ===")
    clients_group = ClientsGroup(
        dataset=args.dataset,
        datadir=args.datadir,
        partition=args.partition,
        n_clients=args.n_clients,
        beta=args.beta,
        device=device,
        train_batch_size=args.train_batch_size,
        test_batch_size=args.test_batch_size
    )
    
    # 创建FedAsync服务器
    print("\n=== 创建FedAsync服务器 ===")
    server = FedAsyncServer(
        model=model,
        device=device,
        n_clients=args.n_clients,
        cfraction=args.cfraction,
        alpha=args.alpha
    )
    
    # 开始联邦学习训练
    print("\n=== 开始FedAsync异步联邦学习训练 ===")
    accuracy_list = []
    start_time = time.time()
    
    for round_num in range(args.comm_rounds):
        print(f"\n--- 第 {round_num + 1}/{args.comm_rounds} 轮通信 ---")
        
        # 选择参与训练的客户端
        selected_clients = server.select_clients(round_num)

        if not selected_clients:
            print("警告：没有客户端参与本轮训练")
            continue

        # 客户端本地训练（异步）
        client_models = []
        client_ids = []

        for client_id in selected_clients:
            client = clients_group.clients_set[client_id]
            
            try:
                # 计算客户端延迟
                staleness = server.global_timestamp - server.client_timestamps[client_id]
                staleness = min(staleness, args.max_staleness)  # 限制最大延迟
                
                # 使用FedAsync算法训练
                updated_params = client.localUpdateFedAsync(
                    model=copy.deepcopy(model),
                    epochs=args.local_epochs,
                    lr=args.lr,
                    global_parameters=server.global_parameters,
                    staleness=staleness
                )

                client_models.append(updated_params)
                client_ids.append(client_id)
                print(f"客户端{client_id}训练完成，延迟: {staleness}")

            except Exception as e:
                print(f"客户端 {client_id} 训练失败: {e}")
                # 清理GPU缓存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                continue
        
        # 服务器异步聚合
        aggregated_params = server.aggregate_models(client_models, client_ids, round_num)
        server.update_global_model(aggregated_params)
        
        # 更新时间戳
        server.global_timestamp += 1
        for client_id in client_ids:
            server.client_timestamps[client_id] = server.global_timestamp
        
        # 测试全局模型
        if (round_num + 1) % 10 == 0 or round_num == args.comm_rounds - 1:
            model.load_state_dict(server.global_parameters)
            accuracy = test_model(model, clients_group.test_loader, device)
            accuracy_list.append(accuracy)
            print(f"第{round_num + 1}轮测试准确率: {accuracy:.4f}")
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 最终测试
    print("\n=== 最终测试 ===")
    model.load_state_dict(server.global_parameters)
    final_accuracy = test_model(model, clients_group.test_loader, device)
    print(f"最终测试准确率: {final_accuracy:.4f}")
    print(f"总训练时间: {total_time:.2f}秒")
    
    # 保存结果
    if args.save_results:
        results = {
            'algorithm': 'FedAsync',
            'final_accuracy': final_accuracy,
            'accuracy_history': accuracy_list,
            'total_time': total_time,
            'config': vars(args)
        }
        
        result_filename = f"fedasync_{args.dataset}_{args.partition}_alpha{args.alpha}.pt"
        result_path = os.path.join(args.logdir, result_filename)
        torch.save(results, result_path)
        print(f"✓ 结果已保存到: {result_path}")


if __name__ == '__main__':
    main()
