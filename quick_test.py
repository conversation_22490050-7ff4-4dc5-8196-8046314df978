#!/usr/bin/env python3
# 快速测试脚本 - 验证融合算法是否能正常运行
import subprocess
import sys
import time
import os

def test_fusion_algorithm():
    """测试融合算法是否能正常启动"""
    print("🧪 快速测试融合算法...")
    
    # 创建必要的目录
    os.makedirs('./data', exist_ok=True)
    os.makedirs('./logs', exist_ok=True)
    
    # 最小化的测试参数
    cmd = [
        sys.executable, 'main.py',
        '--seed', '42',
        '--device', 'cpu',  # 使用CPU避免CUDA问题
        '--datadir', './data',
        '--logdir', './logs',
        '--dataset', 'mnist',  # 使用最简单的数据集
        '--partition', 'iid',  # 使用IID分布
        '--n_clients', '2',    # 最少客户端
        '--cfraction', '1.0',  # 全部参与
        '--local_epochs', '1', # 最少本地训练
        '--comm_rounds', '2',  # 最少通信轮数
        '--lr', '0.01',
        '--model', 'simple-cnn',  # 最简单的模型
        '--model_version', 'optimized',
        '--train_batch_size', '32',
        '--test_batch_size', '32',
        '--use_swim', '1',
        '--out_dim', '64',     # 减小输出维度
        '--temperature', '0.5',
        '--model_buffer_size', '2',  # 减小缓冲区
        '--kr', '0.5'
    ]
    
    print(f"命令: {' '.join(cmd)}")
    print("⏱️  开始测试（最大等待5分钟）...")
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时输出
        start_time = time.time()
        timeout = 300  # 5分钟超时
        
        while True:
            # 检查进程是否结束
            if process.poll() is not None:
                break
                
            # 检查超时
            if time.time() - start_time > timeout:
                print("⏰ 测试超时，终止进程...")
                process.terminate()
                process.wait()
                return False
            
            # 读取输出
            try:
                output = process.stdout.readline()
                if output:
                    print(f"📝 {output.strip()}")
            except:
                pass
            
            time.sleep(0.1)
        
        # 获取最终结果
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            print("✅ 测试成功！融合算法可以正常运行")
            return True
        else:
            print("❌ 测试失败")
            print(f"错误输出: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_imports():
    """测试关键模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        import torchvision
        print(f"✅ TorchVision: {torchvision.__version__}")
        
        # 测试CUDA
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  CUDA不可用，将使用CPU")
        
        # 测试关键模块
        from models import get_model
        print("✅ 模型模块导入成功")

        from datasets import get_dataloader
        print("✅ 数据模块导入成功")
        
        from server import APPAFLServer
        print("✅ 服务器模块导入成功")

        from clients import Client, ClientsGroup
        print("✅ 客户端模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始快速诊断测试")
    print("=" * 50)
    
    # 测试1: 模块导入
    if not test_imports():
        print("❌ 模块导入测试失败，请检查环境配置")
        return
    
    print("\n" + "=" * 50)
    
    # 测试2: 融合算法运行
    if test_fusion_algorithm():
        print("\n🎉 所有测试通过！")
        print("💡 建议：")
        print("  1. 现在可以运行完整的对比实验")
        print("  2. 如果需要使用GPU，确保CUDA环境正确")
        print("  3. 对于大规模实验，建议使用更多的通信轮数")
    else:
        print("\n❌ 融合算法测试失败")
        print("💡 建议：")
        print("  1. 检查数据集是否正确下载")
        print("  2. 检查模型定义是否正确")
        print("  3. 检查参数配置是否匹配")

if __name__ == '__main__':
    main()
