# 综合对比实验脚本
# 同时运行融合算法(SWIM-APPAFL)、FedProx和FedAsync三种算法的对比实验
import subprocess
import os
import time
import argparse
import torch
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import sys


def run_experiment(script_name, config, experiment_name):
    """
    运行单个实验
    Args:
        script_name: 脚本名称
        config: 实验配置字典
        experiment_name: 实验名称
    Returns:
        tuple: (是否成功, 结果文件路径)
    """
    # 构建命令行参数
    cmd = [sys.executable, script_name]
    for key, value in config.items():
        if key != 'experiment_name':
            cmd.extend([f'--{key}', str(value)])
    
    print(f"\n{'='*60}")
    print(f"运行实验: {experiment_name}")
    print(f"脚本: {script_name}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=7200)  # 2小时超时
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✓ 实验成功完成，耗时: {end_time - start_time:.2f}秒")
            
            # 确定结果文件路径
            if 'fedprox' in script_name:
                result_file = f"fedprox_{config['dataset']}_{config['partition']}_mu{config.get('mu', 0.01)}.pt"
            elif 'fedasync' in script_name:
                result_file = f"fedasync_{config['dataset']}_{config['partition']}_alpha{config.get('alpha', 0.5)}.pt"
            else:
                swim_flag = config.get('use_swim', 0)
                result_file = f"results_{config['dataset']}_{config['partition']}_swim{swim_flag}.pt"
            
            result_path = os.path.join(config['logdir'], result_file)
            return True, result_path
        else:
            print(f"✗ 实验失败")
            print(f"错误输出: {result.stderr}")
            return False, None
            
    except subprocess.TimeoutExpired:
        print(f"✗ 实验超时（超过2小时）")
        return False, None
    except Exception as e:
        print(f"✗ 实验异常: {e}")
        return False, None


def load_and_compare_all_results(fusion_path, fedprox_path, fedasync_path, save_path):
    """
    加载并对比三种算法的实验结果
    """
    try:
        # 加载结果
        fusion_results = torch.load(fusion_path)
        fedprox_results = torch.load(fedprox_path)
        fedasync_results = torch.load(fedasync_path)
        
        print(f"\n{'='*80}")
        print(f"三种算法综合对比结果")
        print(f"{'='*80}")
        
        # 提取关键指标
        algorithms = ['FedProx', 'FedAsync', 'SWIM-APPAFL\n(融合算法)']
        accuracies = [
            fedprox_results['final_accuracy'],
            fedasync_results['final_accuracy'],
            fusion_results['final_accuracy']
        ]
        times = [
            fedprox_results['total_time'],
            fedasync_results['total_time'],
            fusion_results['total_time']
        ]
        
        # 打印详细结果
        print(f"算法性能对比:")
        print(f"{'算法':<15} {'准确率':<10} {'训练时间(秒)':<12} {'相对提升':<10}")
        print(f"{'-'*50}")
        
        baseline_acc = fedprox_results['final_accuracy']  # 以FedProx为基准
        for i, (alg, acc, t) in enumerate(zip(algorithms, accuracies, times)):
            improvement = (acc - baseline_acc) / baseline_acc * 100
            alg_clean = alg.replace('\n(融合算法)', '')
            print(f"{alg_clean:<15} {acc:<10.4f} {t:<12.1f} {improvement:+.2f}%")
        
        # 创建综合对比图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 准确率对比
        ax1 = axes[0, 0]
        colors = ['skyblue', 'lightgreen', 'lightcoral']
        bars = ax1.bar(algorithms, accuracies, color=colors)
        ax1.set_ylabel('最终准确率')
        ax1.set_title('准确率对比')
        ax1.set_ylim(0, 1)
        
        # 添加数值标签
        for bar, acc in zip(bars, accuracies):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{acc:.4f}', ha='center', va='bottom')
        
        # 2. 训练时间对比
        ax2 = axes[0, 1]
        bars = ax2.bar(algorithms, times, color=colors)
        ax2.set_ylabel('训练时间 (秒)')
        ax2.set_title('训练时间对比')
        
        # 添加数值标签
        for bar, t in zip(bars, times):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(times)*0.01, 
                    f'{t:.1f}s', ha='center', va='bottom')
        
        # 3. 准确率收敛曲线
        ax3 = axes[1, 0]
        if all('accuracy_history' in results for results in [fusion_results, fedprox_results, fedasync_results]):
            histories = [
                fedprox_results['accuracy_history'],
                fedasync_results['accuracy_history'],
                fusion_results['accuracy_history']
            ]
            
            min_len = min(len(h) for h in histories)
            if min_len > 0:
                rounds = list(range(10, min_len * 10 + 1, 10))
                
                ax3.plot(rounds[:min_len], histories[0][:min_len], 'o-', 
                        label='FedProx', color='skyblue', linewidth=2)
                ax3.plot(rounds[:min_len], histories[1][:min_len], 's-', 
                        label='FedAsync', color='lightgreen', linewidth=2)
                ax3.plot(rounds[:min_len], histories[2][:min_len], '^-', 
                        label='SWIM-APPAFL', color='lightcoral', linewidth=2)
                
                ax3.set_xlabel('通信轮数')
                ax3.set_ylabel('测试准确率')
                ax3.set_title('准确率收敛曲线')
                ax3.legend()
                ax3.grid(True, alpha=0.3)
        
        # 4. 性能雷达图
        ax4 = axes[1, 1]
        
        # 计算归一化指标（越高越好）
        max_acc = max(accuracies)
        min_time = min(times)
        
        normalized_metrics = []
        for acc, t in zip(accuracies, times):
            norm_acc = acc / max_acc  # 准确率归一化
            norm_speed = min_time / t  # 速度归一化（时间越短越好）
            normalized_metrics.append([norm_acc, norm_speed])
        
        # 雷达图数据
        categories = ['准确率', '训练速度']
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        ax4 = plt.subplot(2, 2, 4, projection='polar')
        
        for i, (alg, metrics) in enumerate(zip(['FedProx', 'FedAsync', 'SWIM-APPAFL'], normalized_metrics)):
            values = metrics + metrics[:1]  # 闭合图形
            ax4.plot(angles, values, 'o-', linewidth=2, label=alg, color=colors[i])
            ax4.fill(angles, values, alpha=0.25, color=colors[i])
        
        ax4.set_xticks(angles[:-1])
        ax4.set_xticklabels(categories)
        ax4.set_ylim(0, 1)
        ax4.set_title('综合性能雷达图')
        ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        plt.tight_layout()
        
        # 保存对比图
        plot_path = save_path.replace('.pt', '_comprehensive_comparison.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        print(f"✓ 综合对比图已保存到: {plot_path}")
        plt.close()
        
        # 生成详细对比报告
        comparison_results = {
            'algorithms': {
                'fusion': {
                    'name': 'SWIM-APPAFL',
                    'final_accuracy': fusion_results['final_accuracy'],
                    'training_time': fusion_results['total_time'],
                    'accuracy_history': fusion_results.get('accuracy_history', []),
                    'config': fusion_results['config']
                },
                'fedprox': {
                    'name': 'FedProx',
                    'final_accuracy': fedprox_results['final_accuracy'],
                    'training_time': fedprox_results['total_time'],
                    'accuracy_history': fedprox_results.get('accuracy_history', []),
                    'config': fedprox_results['config']
                },
                'fedasync': {
                    'name': 'FedAsync',
                    'final_accuracy': fedasync_results['final_accuracy'],
                    'training_time': fedasync_results['total_time'],
                    'accuracy_history': fedasync_results.get('accuracy_history', []),
                    'config': fedasync_results['config']
                }
            },
            'rankings': {
                'accuracy': sorted(zip(algorithms, accuracies), key=lambda x: x[1], reverse=True),
                'speed': sorted(zip(algorithms, times), key=lambda x: x[1])
            },
            'improvements': {
                'fusion_vs_fedprox': {
                    'accuracy': fusion_results['final_accuracy'] - fedprox_results['final_accuracy'],
                    'accuracy_percentage': (fusion_results['final_accuracy'] - fedprox_results['final_accuracy']) / fedprox_results['final_accuracy'] * 100,
                    'time_ratio': fusion_results['total_time'] / fedprox_results['total_time']
                },
                'fusion_vs_fedasync': {
                    'accuracy': fusion_results['final_accuracy'] - fedasync_results['final_accuracy'],
                    'accuracy_percentage': (fusion_results['final_accuracy'] - fedasync_results['final_accuracy']) / fedasync_results['final_accuracy'] * 100,
                    'time_ratio': fusion_results['total_time'] / fedasync_results['total_time']
                }
            }
        }
        
        torch.save(comparison_results, save_path)
        print(f"✓ 综合对比结果已保存到: {save_path}")
        
        return comparison_results
        
    except Exception as e:
        print(f"✗ 综合结果对比失败: {e}")
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='三种算法综合对比实验')
    parser.add_argument('--dataset', type=str, default='cifar10', 
                        choices=['cifar10', 'cifar100', 'mnist', 'fashion_mnist'],
                        help='数据集选择')
    parser.add_argument('--quick', action='store_true', help='快速测试模式')
    parser.add_argument('--mu', type=float, default=0.01, help='FedProx正则化参数')
    parser.add_argument('--alpha', type=float, default=0.5, help='FedAsync权重参数')
    args = parser.parse_args()
    
    # 基础配置
    base_config = {
        'seed': 42,
        'device': 'cuda',
        'datadir': './data',
        'logdir': './logs',
        'dataset': args.dataset,
        'partition': 'noniid',
        'beta': 0.5,
        'lr': 0.01,
        'model': 'cnn',
        'model_version': 'optimized',
        'train_batch_size': 64,
        'test_batch_size': 64,
        'save_results': 1
    }
    
    if args.quick:
        base_config.update({
            'n_clients': 10,
            'cfraction': 0.5,
            'local_epochs': 2,
            'comm_rounds': 20,
        })
        print("🚀 快速测试模式")
    else:
        base_config.update({
            'n_clients': 100,
            'cfraction': 0.1,
            'local_epochs': 10,
            'comm_rounds': 100,
        })
        print("🔬 完整实验模式")
    
    os.makedirs(base_config['logdir'], exist_ok=True)
    
    print(f"\n📋 开始三种算法综合对比实验")
    print(f"数据集: {args.dataset}")
    
    # 实验配置
    experiments = [
        {
            'script': 'main.py',
            'config': {**base_config, 'use_swim': 1, 'out_dim': 256, 'temperature': 0.5, 
                      'model_buffer_size': 4, 'kr': 0.5, 'async_weight_strategy': 'local_rounds'},
            'name': 'SWIM-APPAFL融合算法'
        },
        {
            'script': 'main_fedprox.py',
            'config': {**base_config, 'mu': args.mu},
            'name': 'FedProx算法'
        },
        {
            'script': 'main_fedasync.py',
            'config': {**base_config, 'alpha': args.alpha, 'max_staleness': 5},
            'name': 'FedAsync算法'
        }
    ]
    
    # 运行所有实验
    results = []
    for i, exp in enumerate(experiments, 1):
        print(f"\n🔄 步骤 {i}/3: 运行{exp['name']}")
        success, result_path = run_experiment(exp['script'], exp['config'], exp['name'])
        
        if success:
            results.append(result_path)
        else:
            print(f"✗ {exp['name']}实验失败，终止综合对比")
            return
    
    # 综合对比
    print(f"\n🔄 步骤 4/4: 生成综合对比报告")
    comparison_save_path = os.path.join(base_config['logdir'], f'comprehensive_comparison_{args.dataset}.pt')
    comparison_results = load_and_compare_all_results(results[0], results[1], results[2], comparison_save_path)
    
    if comparison_results:
        print(f"\n🎉 综合对比实验完成！")
        print(f"📊 详细结果请查看: {comparison_save_path}")
        
        # 输出排名总结
        print(f"\n🏆 算法排名:")
        print(f"准确率排名:")
        for i, (alg, acc) in enumerate(comparison_results['rankings']['accuracy'], 1):
            alg_clean = alg.replace('\n(融合算法)', '')
            print(f"  {i}. {alg_clean}: {acc:.4f}")
        
        print(f"训练速度排名:")
        for i, (alg, time) in enumerate(comparison_results['rankings']['speed'], 1):
            alg_clean = alg.replace('\n(融合算法)', '')
            print(f"  {i}. {alg_clean}: {time:.1f}秒")
    else:
        print("✗ 综合对比失败")


if __name__ == '__main__':
    main()
